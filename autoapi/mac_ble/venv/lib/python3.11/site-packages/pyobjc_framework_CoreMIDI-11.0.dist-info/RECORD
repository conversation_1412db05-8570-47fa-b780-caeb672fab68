CoreMIDI/_CoreMIDI.cpython-311-darwin.so,sha256=H6KE4sLt2Wyf5BGKGIAlI_UVRBB4dEl_nEX9GyuQTMw,68144
CoreMIDI/__init__.py,sha256=2lBt_DhRcPqw6FVlvf7Zt1i9fo6Hv7iTa8h-KyOt-nQ,1263
CoreMIDI/__pycache__/__init__.cpython-311.pyc,,
CoreMIDI/__pycache__/_metadata.cpython-311.pyc,,
CoreMIDI/_inlines.cpython-311-darwin.so,sha256=5BhQ3X-T6f-lpwT-qURf9LJFf9-dD_oh17LBe93WJp4,88904
CoreMIDI/_metadata.py,sha256=RIopyIhFs2hqUH1_dRXcpi7WtotEHz4DyLK2BXeZ6K4,40748
pyobjc_framework_CoreMIDI-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_CoreMIDI-11.0.dist-info/METADATA,sha256=7WrY24E8g2LgXdJieiS73dZ17YxxaG1YEg_D0bWDVh0,2229
pyobjc_framework_CoreMIDI-11.0.dist-info/RECORD,,
pyobjc_framework_CoreMIDI-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_CoreMIDI-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_CoreMIDI-11.0.dist-info/top_level.txt,sha256=UZ0ZEIYGnujdHtVpJZR1UzimTFKljGpQPO4vp1k4og8,9
