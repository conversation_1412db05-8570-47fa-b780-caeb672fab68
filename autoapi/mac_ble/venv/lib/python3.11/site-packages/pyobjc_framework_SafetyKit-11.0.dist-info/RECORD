SafetyKit/_SafetyKit.cpython-311-darwin.so,sha256=tcZD_zYps2TwF-UzeqEN_BWqTaPcrzqQncRfer7u0qY,68536
SafetyKit/__init__.py,sha256=MS-uR4ifLxyqw3xeHrJro_erNo6FrIza6aZo5mmd_Og,1064
SafetyKit/__pycache__/__init__.cpython-311.pyc,,
SafetyKit/__pycache__/_metadata.cpython-311.pyc,,
SafetyKit/_metadata.py,sha256=6HyiX2wlcDXi1uKSMy31gvrL3GDvaYF_J07rRZLiBp8,3093
pyobjc_framework_SafetyKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_SafetyKit-11.0.dist-info/METADATA,sha256=4kf6Dibesx0Pf1we6sac60nBiHbw_b_X39VYtHCanVU,2290
pyobjc_framework_SafetyKit-11.0.dist-info/RECORD,,
pyobjc_framework_SafetyKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_SafetyKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_SafetyKit-11.0.dist-info/top_level.txt,sha256=3LPAadJHqD7vcluuihynx17aj9T1tkWZiTwhsseA6fk,10
