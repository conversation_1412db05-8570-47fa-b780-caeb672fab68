MetricKit/_MetricKit.cpython-311-darwin.so,sha256=-rHULf-sE_UKMRB-vUCfhGn8NXOGpufpSWVQvyvdpTg,68040
MetricKit/__init__.py,sha256=mgQtlUIavtPyc169Y2wXmvyZa5jzShnBIb3cWoyIuzA,898
MetricKit/__pycache__/__init__.cpython-311.pyc,,
MetricKit/__pycache__/_metadata.cpython-311.pyc,,
MetricKit/_metadata.py,sha256=fP56bZYHO7RZ74L43-6vB-Kuh9SQIaF2tlkxAeGkjfA,1944
pyobjc_framework_MetricKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_MetricKit-11.0.dist-info/METADATA,sha256=b7flaVe-0rITBJEbUR5yXSU3Eph30PbBrHmdmopd48c,2243
pyobjc_framework_MetricKit-11.0.dist-info/RECORD,,
pyobjc_framework_MetricKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_MetricKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_MetricKit-11.0.dist-info/top_level.txt,sha256=GJ0wBiOsBT17WGNBUkgi4vSrh-cmw9F5Mtmsftpzy70,10
