CoreMediaIO/_CoreMediaIO.cpython-311-darwin.so,sha256=YSMVyBEsK1WYIu54urwr8oRDOwm7fySuci02-Wero_s,86744
CoreMediaIO/__init__.py,sha256=Br77QR--LQrMZiFyLaZLm1KpgMShEwQalZpzgFJGJ0w,2533
CoreMediaIO/__pycache__/__init__.cpython-311.pyc,,
CoreMediaIO/__pycache__/_metadata.cpython-311.pyc,,
CoreMediaIO/_metadata.py,sha256=4-JnpAjweQMXsVHQD9kK8sdQQuX_S7qxoY57v7qw2jM,34966
pyobjc_framework_CoreMediaIO-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_CoreMediaIO-11.0.dist-info/METADATA,sha256=aDWDZLt0FVxVxczOlmM5PGWqoDUU0kRDNeuySTrxhsk,2250
pyobjc_framework_CoreMediaIO-11.0.dist-info/RECORD,,
pyobjc_framework_CoreMediaIO-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_CoreMediaIO-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_CoreMediaIO-11.0.dist-info/top_level.txt,sha256=wX1XVgkBWb7BCtTrqZTIpy9KrkSknFMhmtuo8V43ls8,12
