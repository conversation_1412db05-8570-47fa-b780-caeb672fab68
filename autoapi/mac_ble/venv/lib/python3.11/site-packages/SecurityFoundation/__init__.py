"""
Python mapping for the SecurityFoundation framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import Foundation
    import Security
    import objc
    from . import _metadata

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="SecurityFoundation",
        frameworkIdentifier="com.apple.securityfoundation",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/SecurityFoundation.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(
            Security,
            Foundation,
        ),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    del sys.modules["SecurityFoundation._metadata"]


globals().pop("_setup")()
