# This file is generated by objective.metadata
#
# Last update: Tue <PERSON> 11 10:19:45 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$$"""
enums = """$$"""
misc.update({})
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"SFAuthorization",
        b"authorizationWithFlags:rights:environment:",
        {
            "arguments": {
                3: {"type": b"^{_AuthorizationRights=I^{_AuthorizationItem=^cQ^vI}}"},
                4: {
                    "type": b"^{_AuthorizationEnvironment=I^{_AuthorizationItem=^cQ^vI}}"
                },
            }
        },
    )
    r(
        b"SFAuthorization",
        b"initWithFlags:rights:environment:",
        {
            "arguments": {
                3: {"type": b"^{_AuthorizationRights=I^{_AuthorizationItem=^cQ^vI}}"},
                4: {
                    "type": b"^{_AuthorizationEnvironment=I^{_AuthorizationItem=^cQ^vI}}"
                },
            }
        },
    )
    r(
        b"SFAuthorization",
        b"obtainWithRight:flags:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"SFAuthorization",
        b"obtainWithRights:flags:environment:authorizedRights:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"^{_AuthorizationRights=I^{_AuthorizationItem=^cQ^vI}}"},
                4: {
                    "type": b"^{_AuthorizationEnvironment=I^{_AuthorizationItem=^cQ^vI}}"
                },
                5: {
                    "type": b"^^{_AuthorizationRights=I^{_AuthorizationItem=^cQ^vI}}",
                    "type_modifier": b"o",
                },
                6: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"SFAuthorization",
        b"permitWithRights:flags:environment:authorizedRights:",
        {
            "arguments": {
                2: {"type": b"^{_AuthorizationRights=I^{_AuthorizationItem=^cQ^vI}}"},
                4: {
                    "type": b"^{_AuthorizationEnvironment=I^{_AuthorizationItem=^cQ^vI}}"
                },
                5: {"type": b"^{_AuthorizationRights=I^{_AuthorizationItem=^cQ^vI}}"},
            }
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "SFAuthorization", b"initWithFlags:rights:environment:"
)
expressions = {}

# END OF FILE
