ModelIO/_ModelIO.cpython-311-darwin.so,sha256=wOrlnYM4Ps6zB5QimvZVEgg4G1eXDBf9eJnI10IlRII,89728
ModelIO/__init__.py,sha256=MSjC4GYPE8uyUmEMeD1qmIRKcUaMs1YBEKJwkMXFGwg,1179
ModelIO/__pycache__/__init__.cpython-311.pyc,,
ModelIO/__pycache__/_metadata.cpython-311.pyc,,
ModelIO/_metadata.py,sha256=Fd8DRU3Jv2vUntyoeUdN6723EBBvPCx49cj8mtwOnx4,83240
pyobjc_framework_ModelIO-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_ModelIO-11.0.dist-info/METADATA,sha256=EoxDA6qbrFI-P1W2BGoNjLN0HX4Y3mKmJQy04yYl45U,2283
pyobjc_framework_ModelIO-11.0.dist-info/RECORD,,
pyobjc_framework_ModelIO-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_ModelIO-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_ModelIO-11.0.dist-info/top_level.txt,sha256=tlLqg26IgOSRxeNWwXKxJjKC7_NMPPXcKGazzayLCwY,8
