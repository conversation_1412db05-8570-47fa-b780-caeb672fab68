# This file is generated by objective.metadata
#
# Last update: Tu<PERSON> Jun 11 10:21:28 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$UNErrorDomain$UNNotificationAttachmentOptionsThumbnailClippingRectKey$UNNotificationAttachmentOptionsThumbnailHiddenKey$UNNotificationAttachmentOptionsThumbnailTimeKey$UNNotificationAttachmentOptionsTypeHintKey$UNNotificationDefaultActionIdentifier$UNNotificationDismissActionIdentifier$"""
enums = """$UNAlertStyleAlert@2$UNAlertStyleBanner@1$UNAlertStyleNone@0$UNAuthorizationOptionAlert@4$UNAuthorizationOptionAnnouncement@128$UNAuthorizationOptionBadge@1$UNAuthorizationOptionCarPlay@8$UNAuthorizationOptionCriticalAlert@16$UNAuthorizationOptionNone@0$UNAuthorizationOptionProvidesAppNotificationSettings@32$UNAuthorizationOptionProvisional@64$UNAuthorizationOptionSound@2$UNAuthorizationOptionTimeSensitive@256$UNAuthorizationStatusAuthorized@2$UNAuthorizationStatusDenied@1$UNAuthorizationStatusEphemeral@4$UNAuthorizationStatusNotDetermined@0$UNAuthorizationStatusProvisional@3$UNErrorCodeAttachmentCorrupt@105$UNErrorCodeAttachmentInvalidFileSize@102$UNErrorCodeAttachmentInvalidURL@100$UNErrorCodeAttachmentMoveIntoDataStoreFailed@104$UNErrorCodeAttachmentNotInDataStore@103$UNErrorCodeAttachmentUnrecognizedType@101$UNErrorCodeBadgeInputInvalid@1600$UNErrorCodeContentProvidingInvalid@1501$UNErrorCodeContentProvidingObjectNotAllowed@1500$UNErrorCodeNotificationInvalidNoContent@1401$UNErrorCodeNotificationInvalidNoDate@1400$UNErrorCodeNotificationsNotAllowed@1$UNNotificationActionOptionAuthenticationRequired@1$UNNotificationActionOptionDestructive@2$UNNotificationActionOptionForeground@4$UNNotificationActionOptionNone@0$UNNotificationCategoryOptionAllowAnnouncement@16$UNNotificationCategoryOptionAllowInCarPlay@2$UNNotificationCategoryOptionCustomDismissAction@1$UNNotificationCategoryOptionHiddenPreviewsShowSubtitle@8$UNNotificationCategoryOptionHiddenPreviewsShowTitle@4$UNNotificationCategoryOptionNone@0$UNNotificationGroupingSettingDefault@0$UNNotificationGroupingSettingOff@2$UNNotificationGroupingSettingSource@1$UNNotificationInterruptionLevelActive@1$UNNotificationInterruptionLevelCritical@3$UNNotificationInterruptionLevelPassive@0$UNNotificationInterruptionLevelTimeSensitive@2$UNNotificationPresentationOptionAlert@4$UNNotificationPresentationOptionBadge@1$UNNotificationPresentationOptionBanner@16$UNNotificationPresentationOptionList@8$UNNotificationPresentationOptionNone@0$UNNotificationPresentationOptionSound@2$UNNotificationSettingDisabled@1$UNNotificationSettingEnabled@2$UNNotificationSettingNotSupported@0$UNShowPreviewsSettingAlways@0$UNShowPreviewsSettingNever@2$UNShowPreviewsSettingWhenAuthenticated@1$"""
misc.update(
    {
        "UNNotificationCategoryOptions": NewType("UNNotificationCategoryOptions", int),
        "UNAlertStyle": NewType("UNAlertStyle", int),
        "UNNotificationInterruptionLevel": NewType(
            "UNNotificationInterruptionLevel", int
        ),
        "UNAuthorizationOptions": NewType("UNAuthorizationOptions", int),
        "UNNotificationActionOptions": NewType("UNNotificationActionOptions", int),
        "UNShowPreviewsSetting": NewType("UNShowPreviewsSetting", int),
        "UNAuthorizationStatus": NewType("UNAuthorizationStatus", int),
        "UNNotificationSetting": NewType("UNNotificationSetting", int),
        "UNErrorCode": NewType("UNErrorCode", int),
        "UNNotificationPresentationOptions": NewType(
            "UNNotificationPresentationOptions", int
        ),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSObject",
        b"userNotificationCenter:didReceiveNotificationResponse:withCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"userNotificationCenter:openSettingsForNotification:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"userNotificationCenter:willPresentNotification:withCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Q"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"UNCalendarNotificationTrigger",
        b"triggerWithDateMatchingComponents:repeats:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"UNLocationNotificationTrigger",
        b"triggerWithRegion:repeats:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"UNNotificationAttachment",
        b"attachmentWithIdentifier:URL:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"UNNotificationServiceExtension",
        b"didReceiveNotificationRequest:withContentHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNNotificationSettings",
        b"providesAppNotificationSettings",
        {"retval": {"type": "Z"}},
    )
    r(b"UNNotificationTrigger", b"repeats", {"retval": {"type": "Z"}})
    r(
        b"UNTimeIntervalNotificationTrigger",
        b"triggerWithTimeInterval:repeats:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"UNUserNotificationCenter",
        b"addNotificationRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"getDeliveredNotificationsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"getNotificationCategoriesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"getNotificationSettingsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"getPendingNotificationRequestsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"requestAuthorizationWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"setBadgeCount:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"UNUserNotificationCenter",
        b"supportsContentExtensions",
        {"retval": {"type": "Z"}},
    )
finally:
    objc._updatingMetadata(False)
expressions = {}

# END OF FILE
