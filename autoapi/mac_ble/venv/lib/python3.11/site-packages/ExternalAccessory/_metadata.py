# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:10:54 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$EAAccessoryDidConnectNotification$EAAccessoryDidDisconnectNotification$EAAccessoryKey$EAAccessorySelectedKey$EABluetoothAccessoryPickerErrorDomain$"""
enums = """$EABluetoothAccessoryPickerAlreadyConnected@0$EABluetoothAccessoryPickerResultCancelled@2$EABluetoothAccessoryPickerResultFailed@3$EABluetoothAccessoryPickerResultNotFound@1$EAConnectionIDNone@0$EAWiFiUnconfiguredAccessoryBrowserStateConfiguring@3$EAWiFiUnconfiguredAccessoryBrowserStateSearching@2$EAWiFiUnconfiguredAccessoryBrowserStateStopped@1$EAWiFiUnconfiguredAccessoryBrowserStateWiFiUnavailable@0$EAWiFiUnconfiguredAccessoryConfigurationStatusFailed@2$EAWiFiUnconfiguredAccessoryConfigurationStatusSuccess@0$EAWiFiUnconfiguredAccessoryConfigurationStatusUserCancelledConfiguration@1$EAWiFiUnconfiguredAccessoryPropertySupportsAirPlay@1$EAWiFiUnconfiguredAccessoryPropertySupportsAirPrint@2$EAWiFiUnconfiguredAccessoryPropertySupportsHomeKit@4$"""
misc.update(
    {
        "EAWiFiUnconfiguredAccessoryConfigurationStatus": NewType(
            "EAWiFiUnconfiguredAccessoryConfigurationStatus", int
        ),
        "EAWiFiUnconfiguredAccessoryProperties": NewType(
            "EAWiFiUnconfiguredAccessoryProperties", int
        ),
        "EAWiFiUnconfiguredAccessoryBrowserState": NewType(
            "EAWiFiUnconfiguredAccessoryBrowserState", int
        ),
        "EABluetoothAccessoryPickerErrorCode": NewType(
            "EABluetoothAccessoryPickerErrorCode", int
        ),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"EAAccessory", b"isConnected", {"retval": {"type": "Z"}})
    r(b"EAAccessory", b"setConnected:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"EAAccessoryManager",
        b"showBluetoothAccessoryPickerWithNameFilter:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"accessoryBrowser:didFindUnconfiguredAccessories:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accessoryBrowser:didFinishConfiguringAccessory:withStatus:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"accessoryBrowser:didRemoveUnconfiguredAccessories:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accessoryBrowser:didUpdateState:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"accessoryDidDisconnect:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("EASession", b"initWithAccessory:forProtocol:")
objc.registerNewKeywordsFromSelector(
    "EAWiFiUnconfiguredAccessoryBrowser", b"initWithDelegate:queue:"
)
expressions = {}

# END OF FILE
