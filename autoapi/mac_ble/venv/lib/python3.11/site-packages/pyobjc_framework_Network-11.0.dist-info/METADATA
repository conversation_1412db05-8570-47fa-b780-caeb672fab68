Metadata-Version: 2.1
Name: pyobjc-framework-Network
Version: 11.0
Summary: Wrappers for the framework Network on macOS
Home-page: https://github.com/ronaldoussoren/pyobjc
Author: <PERSON>
Author-email: <EMAIL>
License: MIT License
Keywords: PyObjC,Network
Platform: MacOS X (>=10.14)
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X :: Cocoa
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Objective C
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.9
Description-Content-Type: text/x-rst; charset=UTF-8
Project-URL: Documentation, https://pyobjc.readthedocs.io/en/latest/
Project-URL: Issue tracker, https://github.com/ronaldoussoren/pyobjc/issues
Project-URL: Repository, https://github.com/ronaldoussoren/pyobjc
Requires-Dist: pyobjc-core (>=11.0)
Requires-Dist: pyobjc-framework-Cocoa (>=11.0)


Wrappers for the "Network" framework on macOS.

These wrappers don't include documentation, please check Apple's documentation
for information on how to use this framework and PyObjC's documentation
for general tips and tricks regarding the translation between Python
and (Objective-)C frameworks


Project links
-------------

* `Documentation <https://pyobjc.readthedocs.io/en/latest/>`_

* `Issue Tracker <https://github.com/ronaldoussoren/pyobjc/issues>`_

* `Repository <https://github.com/ronaldoussoren/pyobjc/>`_

