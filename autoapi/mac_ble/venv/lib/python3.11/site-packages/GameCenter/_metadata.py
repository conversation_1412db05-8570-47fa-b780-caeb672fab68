# This file is generated by objective.metadata
#
# Last update: Sun Feb 20 18:50:46 2022
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$GKErrorDomain$GKExchangeTimeoutDefault@d$GKExchangeTimeoutNone@d$GKPlayerAuthenticationDidChangeNotificationName$GKPlayerDidChangeNotificationName$GKSessionErrorDomain$GKTurnTimeoutDefault@d$GKTurnTimeoutNone@d$GKVoiceChatServiceErrorDomain$"""
enums = """$GKChallengeStateCompleted@2$GKChallengeStateDeclined@3$GKChallengeStateInvalid@0$GKChallengeStatePending@1$GKErrorAuthenticationInProgress@7$GKErrorCancelled@2$GKErrorChallengeInvalid@19$GKErrorCommunicationsFailure@3$GKErrorGameUnrecognized@15$GKErrorInvalidCredentials@5$GKErrorInvalidParameter@17$GKErrorInvalidPlayer@8$GKErrorInvitationsDisabled@25$GKErrorMatchRequestInvalid@13$GKErrorNotAuthenticated@6$GKErrorNotSupported@16$GKErrorParentalControlsBlocked@10$GKErrorPlayerPhotoFailure@26$GKErrorPlayerStatusExceedsMaximumLength@11$GKErrorPlayerStatusInvalid@12$GKErrorScoreNotSet@9$GKErrorTurnBasedInvalidParticipant@22$GKErrorTurnBasedInvalidState@24$GKErrorTurnBasedInvalidTurn@23$GKErrorTurnBasedMatchDataTooLarge@20$GKErrorTurnBasedTooManySessions@21$GKErrorUbiquityContainerUnavailable@27$GKErrorUnderage@14$GKErrorUnexpectedConnection@18$GKErrorUnknown@1$GKErrorUserDenied@4$GKGameCenterViewControllerStateAchievements@1$GKGameCenterViewControllerStateChallenges@2$GKGameCenterViewControllerStateDefault@-1$GKGameCenterViewControllerStateLeaderboards@0$GKInviteRecipientResponseAccepted@0$GKInviteRecipientResponseDeclined@1$GKInviteRecipientResponseFailed@2$GKInviteRecipientResponseIncompatible@3$GKInviteRecipientResponseNoAnswer@5$GKInviteRecipientResponseUnableToConnect@4$GKInviteeResponseAccepted@0$GKInviteeResponseDeclined@1$GKInviteeResponseFailed@2$GKInviteeResponseIncompatible@3$GKInviteeResponseNoAnswer@5$GKInviteeResponseUnableToConnect@4$GKLeaderboardPlayerScopeFriendsOnly@1$GKLeaderboardPlayerScopeGlobal@0$GKLeaderboardTimeScopeAllTime@2$GKLeaderboardTimeScopeToday@0$GKLeaderboardTimeScopeWeek@1$GKMatchSendDataReliable@0$GKMatchSendDataUnreliable@1$GKMatchTypeHosted@1$GKMatchTypePeerToPeer@0$GKMatchTypeTurnBased@2$GKPeerStateAvailable@0$GKPeerStateConnected@2$GKPeerStateConnecting@4$GKPeerStateDisconnected@3$GKPeerStateUnavailable@1$GKPhotoSizeNormal@1$GKPhotoSizeSmall@0$GKPlayerStateConnected@1$GKPlayerStateDisconnected@2$GKPlayerStateUnknown@0$GKSendDataReliable@0$GKSendDataUnreliable@1$GKSessionCancelledError@30504$GKSessionCannotEnableError@30509$GKSessionConnectionClosedError@30506$GKSessionConnectionFailedError@30505$GKSessionConnectivityError@30201$GKSessionDataTooBigError@30507$GKSessionDeclinedError@30502$GKSessionInProgressError@30510$GKSessionInternalError@30203$GKSessionInvalidParameterError@30500$GKSessionModeClient@1$GKSessionModePeer@2$GKSessionModeServer@0$GKSessionNotConnectedError@30508$GKSessionPeerNotFoundError@30501$GKSessionSystemError@30205$GKSessionTimedOutError@30503$GKSessionTransportError@30202$GKSessionUnknownError@30204$GKTurnBasedExchangeStatusActive@1$GKTurnBasedExchangeStatusCanceled@4$GKTurnBasedExchangeStatusComplete@2$GKTurnBasedExchangeStatusResolved@3$GKTurnBasedExchangeStatusUnknown@0$GKTurnBasedMatchOutcomeCustomRange@16711680$GKTurnBasedMatchOutcomeFirst@6$GKTurnBasedMatchOutcomeFourth@9$GKTurnBasedMatchOutcomeLost@3$GKTurnBasedMatchOutcomeNone@0$GKTurnBasedMatchOutcomeQuit@1$GKTurnBasedMatchOutcomeSecond@7$GKTurnBasedMatchOutcomeThird@8$GKTurnBasedMatchOutcomeTied@4$GKTurnBasedMatchOutcomeTimeExpired@5$GKTurnBasedMatchOutcomeWon@2$GKTurnBasedMatchStatusEnded@2$GKTurnBasedMatchStatusMatching@3$GKTurnBasedMatchStatusOpen@1$GKTurnBasedMatchStatusUnknown@0$GKTurnBasedParticipantStatusActive@4$GKTurnBasedParticipantStatusDeclined@2$GKTurnBasedParticipantStatusDone@5$GKTurnBasedParticipantStatusInvited@1$GKTurnBasedParticipantStatusMatching@3$GKTurnBasedParticipantStatusUnknown@0$GKVoiceChatPlayerConnected@0$GKVoiceChatPlayerConnecting@4$GKVoiceChatPlayerDisconnected@1$GKVoiceChatPlayerSilent@3$GKVoiceChatPlayerSpeaking@2$GKVoiceChatServiceAudioUnavailableError@32005$GKVoiceChatServiceClientMissingRequiredMethodsError@32007$GKVoiceChatServiceInternalError@32000$GKVoiceChatServiceInvalidCallIDError@32004$GKVoiceChatServiceInvalidParameterError@32016$GKVoiceChatServiceMethodCurrentlyInvalidError@32012$GKVoiceChatServiceNetworkConfigurationError@32013$GKVoiceChatServiceNoRemotePacketsError@32001$GKVoiceChatServiceOutOfMemoryError@32015$GKVoiceChatServiceRemoteParticipantBusyError@32008$GKVoiceChatServiceRemoteParticipantCancelledError@32009$GKVoiceChatServiceRemoteParticipantDeclinedInviteError@32011$GKVoiceChatServiceRemoteParticipantHangupError@32003$GKVoiceChatServiceRemoteParticipantResponseInvalidError@32010$GKVoiceChatServiceUnableToConnectError@32002$GKVoiceChatServiceUninitializedClientError@32006$GKVoiceChatServiceUnsupportedRemoteVersionError@32014$"""
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"GKAchievement",
        b"challengeComposeControllerWithMessage:players:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"GKAchievement", b"isCompleted", {"retval": {"type": "Z"}})
    r(b"GKAchievement", b"isHidden", {"retval": {"type": "Z"}})
    r(
        b"GKAchievement",
        b"loadAchievementsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"reportAchievementWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"reportAchievements:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"reportAchievements:withEligibleChallenges:withCompletionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"resetAchievementsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"selectChallengeablePlayerIDs:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"selectChallengeablePlayers:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"GKAchievement", b"setShowsCompletionBanner:", {"arguments": {2: {"type": "Z"}}})
    r(b"GKAchievement", b"showsCompletionBanner", {"retval": {"type": "Z"}})
    r(b"GKAchievementDescription", b"isHidden", {"retval": {"type": "Z"}})
    r(b"GKAchievementDescription", b"isReplayable", {"retval": {"type": "Z"}})
    r(
        b"GKAchievementDescription",
        b"loadAchievementDescriptionsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKAchievementDescription",
        b"loadImageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKChallenge",
        b"loadReceivedChallengesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"GKDialogController", b"presentViewController:", {"retval": {"type": "Z"}})
    r(b"GKInvite", b"isHosted", {"retval": {"type": "Z"}})
    r(b"GKInvite", b"setHosted:", {"arguments": {2: {"type": "Z"}}})
    r(b"GKLeaderboard", b"isLoading", {"retval": {"type": "Z"}})
    r(
        b"GKLeaderboard",
        b"loadCategoriesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadImageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadLeaderboardsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadScoresWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"setDefaultLeaderboard:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadImageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadLeaderboardSetsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadLeaderboardsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"authenticateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"authenticateWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"deleteSavedGamesWithName:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"fetchSavedGamesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"generateIdentityVerificationSignatureWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": sel32or64(b"I", b"Q")},
                            5: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"GKLocalPlayer", b"isAuthenticated", {"retval": {"type": "Z"}})
    r(
        b"GKLocalPlayer",
        b"loadDefaultLeaderboardCategoryIDWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadDefaultLeaderboardIdentifierWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriendPlayersWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriendsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadLeaderboardSetsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"resolveConflictingSavedGames:withData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"saveGameData:withName:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"setAuthenticateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"setDefaultLeaderboardCategoryID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"setDefaultLeaderboardIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"chooseBestHostPlayerWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"chooseBestHostingPlayerWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"rematchWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"sendData:toPlayers:dataMode:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"GKMatch",
        b"sendData:toPlayers:withDataMode:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"GKMatch",
        b"sendDataToAllPlayers:withDataMode:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"GKMatchRequest",
        b"inviteeResponseHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": sel32or64(b"I", b"Q")},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(
        b"GKMatchRequest",
        b"recipientResponseHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": sel32or64(b"I", b"Q")},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(
        b"GKMatchRequest",
        b"setInviteeResponseHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": sel32or64(b"I", b"Q")},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchRequest",
        b"setRecipientResponseHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": sel32or64(b"I", b"Q")},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"addPlayersToMatch:matchRequest:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findMatchForRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findPlayersForHostedMatchRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findPlayersForHostedRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"inviteHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"matchForInvite:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"queryActivityWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": sel32or64(b"i", b"q")},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"queryPlayerGroupActivity:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": sel32or64(b"i", b"q")},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"setInviteHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"startBrowsingForNearbyPlayersWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"startBrowsingForNearbyPlayersWithReachableHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"GKMatchmakerViewController", b"isHosted", {"retval": {"type": "Z"}})
    r(b"GKMatchmakerViewController", b"setHosted:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"GKMatchmakerViewController",
        b"setHostedPlayer:connected:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"GKMatchmakerViewController",
        b"setHostedPlayer:didConnect:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"GKMatchmakerViewController",
        b"setShowExistingMatches:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"GKMatchmakerViewController", b"showExistingMatches", {"retval": {"type": "Z"}})
    r(
        b"GKNotificationBanner",
        b"showBannerWithTitle:message:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKNotificationBanner",
        b"showBannerWithTitle:message:duration:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKPlayer",
        b"loadPhotoForSize:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKPlayer",
        b"loadPlayersForIdentifiers:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKSavedGame",
        b"loadDataWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKScore",
        b"challengeComposeControllerWithMessage:players:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportScoreWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportScores:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportScores:withEligibleChallenges:withCompletionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"GKScore", b"setShouldSetDefaultLeaderboard:", {"arguments": {2: {"type": "Z"}}})
    r(b"GKScore", b"shouldSetDefaultLeaderboard", {"retval": {"type": "Z"}})
    r(
        b"GKSession",
        b"acceptConnectionFromPeer:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"GKSession", b"isActive", {"retval": {"type": "Z"}})
    r(b"GKSession", b"isAvailable", {"retval": {"type": "Z"}})
    r(
        b"GKSession",
        b"sendData:toPeers:withDataMode:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"GKSession",
        b"sendDataToAllPeers:withDataMode:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"GKSession", b"setAvailable:", {"arguments": {2: {"type": "Z"}}})
    r(b"GKSession", b"setIsActive:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"GKTurnBasedExchange",
        b"cancelWithLocalizableMessageKey:arguments:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedExchange",
        b"replyWithLocalizableMessageKey:arguments:data:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedExchange",
        b"setShowExistingMatches:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"GKTurnBasedExchange", b"showExistingMatches", {"retval": {"type": "Z"}})
    r(
        b"GKTurnBasedMatch",
        b"acceptInviteWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"declineInviteWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endMatchInTurnWithMatchData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endMatchInTurnWithMatchData:scores:achievements:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endTurnWithNextParticipant:matchData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endTurnWithNextParticipants:turnTimeout:matchData:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"findMatchForRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"loadMatchDataWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"loadMatchWithID:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"loadMatchesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"participantQuitInTurnWithOutcome:nextParticipant:matchData:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"participantQuitInTurnWithOutcome:nextParticipants:turnTimeout:matchData:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"participantQuitOutOfTurnWithOutcome:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"rematchWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"removeWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"saveCurrentTurnWithMatchData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"saveMergedMatchData:withResolvedExchanges:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"sendExchangeToParticipants:data:localizableMessageKey:arguments:timeout:completionHandler:",
        {
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"sendReminderToParticipants:localizableMessageKey:arguments:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatchmakerViewController",
        b"setShowExistingMatches:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"GKTurnBasedMatchmakerViewController",
        b"showExistingMatches",
        {"retval": {"type": "Z"}},
    )
    r(b"GKVoiceChat", b"isActive", {"retval": {"type": "Z"}})
    r(b"GKVoiceChat", b"isVoIPAllowed", {"retval": {"type": "Z"}})
    r(
        b"GKVoiceChat",
        b"playerStateUpdateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": sel32or64(b"I", b"Q")},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(
        b"GKVoiceChat",
        b"playerVoiceChatStateDidChangeHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(b"GKVoiceChat", b"setActive:", {"arguments": {2: {"type": "Z"}}})
    r(b"GKVoiceChat", b"setMute:forPlayer:", {"arguments": {2: {"type": "Z"}}})
    r(b"GKVoiceChat", b"setPlayer:muted:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"GKVoiceChat",
        b"setPlayerStateUpdateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": sel32or64(b"I", b"Q")},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKVoiceChat",
        b"setPlayerVoiceChatStateDidChangeHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"handleTurnEventForMatch:didBecomeActive:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSObject",
        b"match:player:didChangeConnectionState:",
        {"arguments": {4: {"type": sel32or64(b"I", b"Q")}}},
    )
    r(
        b"NSObject",
        b"match:player:didChangeState:",
        {"arguments": {4: {"type": sel32or64(b"I", b"Q")}}},
    )
    r(
        b"NSObject",
        b"match:shouldReinviteDisconnectedPlayer:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSObject", b"match:shouldReinvitePlayer:", {"retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"player:receivedTurnEventForMatch:didBecomeActive:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSObject",
        b"session:peer:didChangeState:",
        {"arguments": {4: {"type": sel32or64(b"I", b"Q")}}},
    )
    r(
        b"NSObject",
        b"shouldShowBannerForLocallyCompletedChallenge:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"shouldShowBannerForLocallyReceivedChallenge:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"shouldShowBannerForRemotelyCompletedChallenge:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"voiceChatService:didReceiveInvitationFromParticipantID:callID:",
        {"arguments": {4: {"type": sel32or64(b"I", b"Q")}}},
    )
finally:
    objc._updatingMetadata(False)
expressions = {}

# END OF FILE
