CFNetwork/__init__.py,sha256=7a7L-aVgktmWoJMwdna1YFtMIKZJT4hkg40pdPGHb9Q,1167
CFNetwork/__pycache__/__init__.cpython-311.pyc,,
CFNetwork/__pycache__/_metadata.cpython-311.pyc,,
CFNetwork/_manual.cpython-311-darwin.so,sha256=IAr_P47laP1e03-hOVBRATtxLKFzqGJPdgKQZ4ednHU,87408
CFNetwork/_metadata.py,sha256=aPGCVmWmTOwsNrlcZOv-rPRgrbr68e53udCZqaVMj7k,26348
pyobjc_framework_CFNetwork-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_CFNetwork-11.0.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_CFNetwork-11.0.dist-info/METADATA,sha256=TptZq9gDZiZR18SpxBg8nAEhJ_gRPYpCfMtf_27ZSNQ,2522
pyobjc_framework_CFNetwork-11.0.dist-info/RECORD,,
pyobjc_framework_CFNetwork-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_CFNetwork-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_CFNetwork-11.0.dist-info/top_level.txt,sha256=q2Z9JhHHv2Q8zzn9XMN_mb4IrMeGTqi4Ic62XPVuKGA,10
