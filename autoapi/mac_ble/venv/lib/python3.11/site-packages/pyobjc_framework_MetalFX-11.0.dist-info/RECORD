MetalFX/_MetalFX.cpython-311-darwin.so,sha256=dJDfOMkpX2xEvuHFehAgLCI4A2CXwNdy8Cq1PwNI2Ic,85088
MetalFX/__init__.py,sha256=UobbQj9274xgtMRaOu6WJMZ7QP4AnAG0pOiNta8SnrY,874
MetalFX/__pycache__/__init__.cpython-311.pyc,,
MetalFX/__pycache__/_metadata.cpython-311.pyc,,
MetalFX/_metadata.py,sha256=q12KmQ0CoSyq_8q4AuRLQcFtbqGxlnxLTO664aR1wcc,7591
pyobjc_framework_MetalFX-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_MetalFX-11.0.dist-info/METADATA,sha256=diGEx6KSs9LdDSX6Tg5OXNaf85cDLCCAcVNeEM8JaZQ,2234
pyobjc_framework_MetalFX-11.0.dist-info/RECORD,,
pyobjc_framework_MetalFX-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_MetalFX-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_MetalFX-11.0.dist-info/top_level.txt,sha256=LVGn6NujDh_YlQlhwAJAfVpSgyS_mVmAzEBBPgduHIE,8
