Metadata-Version: 2.2
Name: pyobjc
Version: 11.0
Summary: Python<->ObjC Interoperability Module
Home-page: https://github.com/ronal<PERSON><PERSON>oren/pyobjc
Author: <PERSON>
Author-email: <EMAIL>
License: MIT License
Keywords: Objective-C,bridge,Cocoa
Platform: macOS
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X :: Cocoa
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Objective C
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.9
Description-Content-Type: text/x-rst; charset=UTF-8
Requires-Dist: pyobjc-core==11.0
Requires-Dist: pyobjc-framework-libdispatch==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-libxpc==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-Accessibility==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-AdServices==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-AdSupport==11.0; platform_release >= "18.0"
Requires-Dist: pyobjc-framework-AppTrackingTransparency==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-AudioVideoBridging==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-AuthenticationServices==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-AutomaticAssessmentConfiguration==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-AVKit==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-AVFoundation==11.0; platform_release >= "11.0"
Requires-Dist: pyobjc-framework-AVRouting==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-Accounts==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-AddressBook==11.0
Requires-Dist: pyobjc-framework-AppleScriptKit==11.0
Requires-Dist: pyobjc-framework-AppleScriptObjC==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-ApplicationServices==11.0
Requires-Dist: pyobjc-framework-Automator==11.0
Requires-Dist: pyobjc-framework-BackgroundAssets==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-BrowserEngineKit==11.0; platform_release >= "23.4"
Requires-Dist: pyobjc-framework-BusinessChat==11.0; platform_release >= "18.0"
Requires-Dist: pyobjc-framework-CFNetwork==11.0
Requires-Dist: pyobjc-framework-CalendarStore==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-CallKit==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-Carbon==11.0
Requires-Dist: pyobjc-framework-Cinematic==11.0; platform_release >= "23.0"
Requires-Dist: pyobjc-framework-ClassKit==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-CloudKit==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-Cocoa==11.0
Requires-Dist: pyobjc-framework-Collaboration==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-ColorSync==11.0; platform_release >= "17.0"
Requires-Dist: pyobjc-framework-Contacts==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-ContactsUI==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-CoreAudio==11.0
Requires-Dist: pyobjc-framework-CoreAudioKit==11.0
Requires-Dist: pyobjc-framework-CoreBluetooth==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-CoreData==11.0
Requires-Dist: pyobjc-framework-CoreHaptics==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-CoreLocation==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-CoreMedia==11.0; platform_release >= "11.0"
Requires-Dist: pyobjc-framework-CoreMediaIO==11.0; platform_release >= "11.0"
Requires-Dist: pyobjc-framework-CoreMIDI==11.0
Requires-Dist: pyobjc-framework-CoreML==11.0; platform_release >= "17.0"
Requires-Dist: pyobjc-framework-CoreMotion==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-CoreServices==11.0
Requires-Dist: pyobjc-framework-CoreSpotlight==11.0; platform_release >= "17.0"
Requires-Dist: pyobjc-framework-CoreText==11.0
Requires-Dist: pyobjc-framework-CoreWLAN==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-CryptoTokenKit==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-DataDetection==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-DeviceCheck==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-DeviceDiscoveryExtension==11.0; platform_release >= "24.0"
Requires-Dist: pyobjc-framework-DictionaryServices==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-DiscRecording==11.0
Requires-Dist: pyobjc-framework-DiscRecordingUI==11.0
Requires-Dist: pyobjc-framework-DiskArbitration==11.0
Requires-Dist: pyobjc-framework-DVDPlayback==11.0
Requires-Dist: pyobjc-framework-EventKit==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-ExceptionHandling==11.0
Requires-Dist: pyobjc-framework-ExecutionPolicy==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-ExternalAccessory==11.0; platform_release >= "17.0"
Requires-Dist: pyobjc-framework-ExtensionKit==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-FileProvider==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-FileProviderUI==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-FSEvents==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-FinderSync==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-GameCenter==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-GameController==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-HealthKit==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-InputMethodKit==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-ImageCaptureCore==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-Intents==11.0; platform_release >= "16.0"
Requires-Dist: pyobjc-framework-IntentsUI==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-InstallerPlugins==11.0
Requires-Dist: pyobjc-framework-InstantMessage==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-IOBluetooth==11.0
Requires-Dist: pyobjc-framework-IOBluetoothUI==11.0
Requires-Dist: pyobjc-framework-IOSurface==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-KernelManagement==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-LatentSemanticMapping==11.0
Requires-Dist: pyobjc-framework-LaunchServices==11.0
Requires-Dist: pyobjc-framework-LinkPresentation==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-LocalAuthentication==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-LocalAuthenticationEmbeddedUI==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-MailKit==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-MapKit==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-MediaAccessibility==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-MediaExtension==11.0; platform_release >= "24.0"
Requires-Dist: pyobjc-framework-MediaLibrary==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-MediaPlayer==11.0; platform_release >= "16.0"
Requires-Dist: pyobjc-framework-MediaToolbox==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-Metal==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-MetalFX==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-MetalKit==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-MetalPerformanceShaders==11.0; platform_release >= "17.0"
Requires-Dist: pyobjc-framework-MetalPerformanceShadersGraph==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-MetricKit==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-MLCompute==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-ModelIO==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-MultipeerConnectivity==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-NaturalLanguage==11.0; platform_release >= "18.0"
Requires-Dist: pyobjc-framework-NetFS==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-Network==11.0; platform_release >= "18.0"
Requires-Dist: pyobjc-framework-NetworkExtension==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-NotificationCenter==11.0; platform_release >= "14.0"
Requires-Dist: pyobjc-framework-OpenDirectory==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-OSAKit==11.0
Requires-Dist: pyobjc-framework-OSLog==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-PassKit==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-PencilKit==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-PHASE==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-Photos==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-PhotosUI==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-PreferencePanes==11.0
Requires-Dist: pyobjc-framework-PubSub==11.0; platform_release >= "9.0" and platform_release < "18.0"
Requires-Dist: pyobjc-framework-PushKit==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-Quartz==11.0
Requires-Dist: pyobjc-framework-QuickLookThumbnailing==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-ReplayKit==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-SafetyKit==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-SafariServices==11.0; platform_release >= "16.0"
Requires-Dist: pyobjc-framework-ScreenSaver==11.0
Requires-Dist: pyobjc-framework-ScreenTime==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-ScriptingBridge==11.0; platform_release >= "9.0"
Requires-Dist: pyobjc-framework-Security==11.0
Requires-Dist: pyobjc-framework-SecurityFoundation==11.0
Requires-Dist: pyobjc-framework-SecurityInterface==11.0
Requires-Dist: pyobjc-framework-SearchKit==11.0
Requires-Dist: pyobjc-framework-ServiceManagement==11.0; platform_release >= "10.0"
Requires-Dist: pyobjc-framework-ShazamKit==11.0; platform_release >= "21.0"
Requires-Dist: pyobjc-framework-Social==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-Speech==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-SpriteKit==11.0; platform_release >= "13.0"
Requires-Dist: pyobjc-framework-StoreKit==11.0; platform_release >= "11.0"
Requires-Dist: pyobjc-framework-SyncServices==11.0
Requires-Dist: pyobjc-framework-SystemConfiguration==11.0
Requires-Dist: pyobjc-framework-WebKit==11.0
Requires-Dist: pyobjc-framework-GameKit==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-GameplayKit==11.0; platform_release >= "15.0"
Requires-Dist: pyobjc-framework-SceneKit==11.0; platform_release >= "11.0"
Requires-Dist: pyobjc-framework-SensitiveContentAnalysis==11.0; platform_release >= "23.0"
Requires-Dist: pyobjc-framework-SharedWithYouCore==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-SharedWithYou==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-SoundAnalysis==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-ScreenCaptureKit==11.0; platform_release >= "21.4"
Requires-Dist: pyobjc-framework-Symbols==11.0; platform_release >= "23.0"
Requires-Dist: pyobjc-framework-SystemExtensions==11.0; platform_release >= "19.0"
Requires-Dist: pyobjc-framework-ThreadNetwork==11.0; platform_release >= "22.0"
Requires-Dist: pyobjc-framework-UniformTypeIdentifiers==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-UserNotifications==11.0; platform_release >= "18.0"
Requires-Dist: pyobjc-framework-UserNotificationsUI==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-VideoSubscriberAccount==11.0; platform_release >= "18.0"
Requires-Dist: pyobjc-framework-VideoToolbox==11.0; platform_release >= "12.0"
Requires-Dist: pyobjc-framework-Virtualization==11.0; platform_release >= "20.0"
Requires-Dist: pyobjc-framework-Vision==11.0; platform_release >= "17.0"
Requires-Dist: pyobjc-framework-iTunesLibrary==11.0; platform_release >= "10.0"
Provides-Extra: allbindings
Requires-Dist: pyobjc-core==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-libdispatch==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-libxpc==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Accessibility==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AdServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AdSupport==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AppTrackingTransparency==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AudioVideoBridging==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AuthenticationServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AutomaticAssessmentConfiguration==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AVKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AVFoundation==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AVRouting==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Accounts==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AddressBook==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AppleScriptKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-AppleScriptObjC==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ApplicationServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Automator==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-BackgroundAssets==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-BrowserEngineKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-BusinessChat==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CFNetwork==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CalendarStore==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CallKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Carbon==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Cinematic==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ClassKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CloudKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Cocoa==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Collaboration==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ColorSync==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Contacts==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ContactsUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreAudio==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreAudioKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreBluetooth==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreData==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreHaptics==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreLocation==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreMedia==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreMediaIO==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreMIDI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreML==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreMotion==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreSpotlight==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreText==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CoreWLAN==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-CryptoTokenKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DataDetection==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DeviceCheck==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DeviceDiscoveryExtension==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DictionaryServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DiscRecording==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DiscRecordingUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DiskArbitration==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-DVDPlayback==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-EventKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ExceptionHandling==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ExecutionPolicy==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ExternalAccessory==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ExtensionKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-FileProvider==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-FileProviderUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-FSEvents==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-FinderSync==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-GameCenter==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-GameController==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-HealthKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-InputMethodKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ImageCaptureCore==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Intents==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-IntentsUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-InstallerPlugins==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-InstantMessage==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-IOBluetooth==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-IOBluetoothUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-IOSurface==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-KernelManagement==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-LatentSemanticMapping==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-LaunchServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-LinkPresentation==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-LocalAuthentication==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-LocalAuthenticationEmbeddedUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MailKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MapKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MediaAccessibility==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MediaExtension==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MediaLibrary==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MediaPlayer==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MediaToolbox==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Metal==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MetalFX==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MetalKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MetalPerformanceShaders==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MetalPerformanceShadersGraph==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MetricKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MLCompute==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ModelIO==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-MultipeerConnectivity==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-NaturalLanguage==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-NetFS==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Network==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-NetworkExtension==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-NotificationCenter==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-OpenDirectory==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-OSAKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-OSLog==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PassKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PencilKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PHASE==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Photos==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PhotosUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PreferencePanes==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PubSub==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-PushKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Quartz==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-QuickLookThumbnailing==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ReplayKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SafetyKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SafariServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ScreenSaver==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ScreenTime==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ScriptingBridge==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Security==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SecurityFoundation==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SecurityInterface==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SearchKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ServiceManagement==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ShazamKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Social==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Speech==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SpriteKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-StoreKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SyncServices==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SystemConfiguration==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-WebKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-GameKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-GameplayKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SceneKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SensitiveContentAnalysis==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SharedWithYouCore==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SharedWithYou==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SoundAnalysis==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ScreenCaptureKit==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Symbols==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-SystemExtensions==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-ThreadNetwork==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-UniformTypeIdentifiers==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-UserNotifications==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-UserNotificationsUI==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-VideoSubscriberAccount==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-VideoToolbox==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Virtualization==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-Vision==11.0; extra == "allbindings"
Requires-Dist: pyobjc-framework-iTunesLibrary==11.0; extra == "allbindings"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary
Project-URL: Documentation, https://pyobjc.readthedocs.io/en/latest/
Project-URL: Issue tracker, https://github.com/ronaldoussoren/pyobjc/issues
Project-URL: Repository, https://github.com/ronaldoussoren/pyobjc


PyObjC is a bridge between Python and Objective-C.  It allows full
featured Cocoa applications to be written in pure Python.  It is also
easy to use other frameworks containing Objective-C class libraries
from Python and to mix in Objective-C, C and C++ source.

This package is a pseudo-package that will install all pyobjc related
packages (that is, pyobjc-core as well as wrappers for frameworks on
macOS)

Project links
-------------

* `Documentation <https://pyobjc.readthedocs.io/en/latest/>`_
* `Issue Tracker <https://github.com/ronaldoussoren/pyobjc/issues>`_
* `Repository <https://github.com/ronaldoussoren/pyobjc/>`_
