CoreText/__init__.py,sha256=dU-9MTRBDKyRmTQttctAiqsM1DWcn-wFhCrJe9xeG48,886
CoreText/__pycache__/__init__.cpython-311.pyc,,
CoreText/__pycache__/_metadata.cpython-311.pyc,,
CoreText/_manual.cpython-311-darwin.so,sha256=jp3b-fV3Uq0V5W44VZf8RDjCbTVZVONnNhxjvLo4AXY,87584
CoreText/_metadata.py,sha256=22dJxZbTrj173mkoAJjG_XOdDk-HO0OIEIOwKoPO-8U,78471
pyobjc_framework_CoreText-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_CoreText-11.0.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_CoreText-11.0.dist-info/METADATA,sha256=G3Xmt832f2GlONF9H7sGZm-8k6mjQZXcGdNNh-vK1Fg,2466
pyobjc_framework_CoreText-11.0.dist-info/RECORD,,
pyobjc_framework_CoreText-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_CoreText-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_CoreText-11.0.dist-info/top_level.txt,sha256=el7Md_M_JEGtUwR5FVoJ75sdEa89FGQ8-h7eR2u8pAo,9
