IOSurface/__init__.py,sha256=oqy0wryYdQg9kC89IrWDNCvVXUCUYjV31HI-SZ9MnBY,840
IOSurface/__pycache__/__init__.cpython-311.pyc,,
IOSurface/__pycache__/_metadata.cpython-311.pyc,,
IOSurface/_metadata.py,sha256=Yt3kidBY1tty7q-uR7GLTbk_40FXeKfb9Zb1ZK7PL88,9323
pyobjc_framework_IOSurface-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_IOSurface-11.0.dist-info/METADATA,sha256=i_oKp2II5ojZosjtzjYf5p_ougCRNCsnn0YvZuRRuDM,2486
pyobjc_framework_IOSurface-11.0.dist-info/RECORD,,
pyobjc_framework_IOSurface-11.0.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
pyobjc_framework_IOSurface-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_IOSurface-11.0.dist-info/top_level.txt,sha256=lEU3WSYCMN0qfpwvdH0uiFRXCDH2J0TOqhJM3Vz-ozI,10
