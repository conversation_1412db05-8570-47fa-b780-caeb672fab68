dispatch/__init__.py,sha256=qZKO_3DaXhgnDQrbnu1IT-frPEZsGcJcslZYJp8KPHE,812
dispatch/__pycache__/__init__.cpython-311.pyc,,
dispatch/__pycache__/_metadata.cpython-311.pyc,,
dispatch/_dispatch.cpython-311-darwin.so,sha256=qyk0JDR0MM4bL3ReLwDwDqtjep7wSyuYe8fGWoWjYmE,87144
dispatch/_inlines.cpython-311-darwin.so,sha256=OCXCcyaRb27v16ZZiX7vYqvi4Tn1tr-aFnwm_8xUgfg,67312
dispatch/_metadata.py,sha256=aVd6XwxiJLuyEzajuwIAvMGnXfzzH3yvioHb7p4yHAU,29504
libdispatch/__init__.py,sha256=eMQlZbYsUyi-dc70BIT3FYme5Y0xDNldj9c_0XtsQJE,621
libdispatch/__pycache__/__init__.cpython-311.pyc,,
pyobjc_framework_libdispatch-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_libdispatch-11.0.dist-info/METADATA,sha256=KyFU51awAcEFC8CPnFdCHAQFv82GCEmugSMCzc53fCc,2240
pyobjc_framework_libdispatch-11.0.dist-info/RECORD,,
pyobjc_framework_libdispatch-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_libdispatch-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_libdispatch-11.0.dist-info/top_level.txt,sha256=9B7EZ9lZzS6f7qyqtzyUlVjdhJBdkQMc6yPV18WG7Zs,21
