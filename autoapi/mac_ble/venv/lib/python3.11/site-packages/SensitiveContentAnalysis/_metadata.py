# This file is generated by objective.metadata
#
# Last update: Tue <PERSON> 11 10:19:56 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$$"""
enums = """$SCSensitivityAnalysisPolicyDescriptiveInterventions@2$SCSensitivityAnalysisPolicyDisabled@0$SCSensitivityAnalysisPolicySimpleInterventions@1$"""
misc.update(
    {"SCSensitivityAnalysisPolicy": NewType("SCSensitivityAnalysisPolicy", int)}
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"SCSensitivityAnalysis", b"isSensitive", {"retval": {"type": b"Z"}})
    r(
        b"SCSensitivityAnalyzer",
        b"analyzeCGImage:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCSensitivityAnalyzer",
        b"analyzeImageFile:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SCSensitivityAnalyzer",
        b"analyzeVideoFile:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
finally:
    objc._updatingMetadata(False)
expressions = {}

# END OF FILE
