HealthKit/_HealthKit.cpython-311-darwin.so,sha256=9YZ-VfyUuqz7DV2mzrD4XOIbbZrxorGVEJbd_cK8h8s,68744
HealthKit/__init__.py,sha256=3TupTAAPkcCXUoaF4zEqUIfgaN49kOPRu_fVPTF6FxE,3610
HealthKit/__pycache__/__init__.cpython-311.pyc,,
HealthKit/__pycache__/_metadata.cpython-311.pyc,,
HealthKit/_metadata.py,sha256=HfB2kFBXtMb32l3lpuGquXydGJFN4WVHBa-S8CRgkcQ,95079
pyobjc_framework_HealthKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_HealthKit-11.0.dist-info/METADATA,sha256=F3duSh-Vekzm5gX2mY3L_TYmy11jL_dwHoZCNJz-mNY,2242
pyobjc_framework_HealthKit-11.0.dist-info/RECORD,,
pyobjc_framework_HealthKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_HealthKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_HealthKit-11.0.dist-info/top_level.txt,sha256=kZrFKkp12n0yEMN2Wly_1QeLiXC1A-EKHDatN9sB_8U,10
