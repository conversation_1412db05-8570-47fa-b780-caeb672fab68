ScreenSaver/__init__.py,sha256=ZnjBYxLKpdnL9_YEgeL96GXaVUjHNeFdOLFfcaDOGsg,891
ScreenSaver/__pycache__/__init__.cpython-311.pyc,,
ScreenSaver/__pycache__/_metadata.cpython-311.pyc,,
ScreenSaver/_inlines.cpython-311-darwin.so,sha256=JpChIaCphTvYr_--JIHhQFUjNdapl4MUqkyfdLjSXPc,67616
ScreenSaver/_metadata.py,sha256=QikmDRaoLvFHQz3Eyyd0X2yu2ZSNmw5CEUKAtQyccgs,1549
pyobjc_framework_ScreenSaver-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_ScreenSaver-11.0.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_ScreenSaver-11.0.dist-info/METADATA,sha256=OqH6v6NxuQiRqNaHR_uoGcGI_Cykk11iM5yQbULGwtY,2331
pyobjc_framework_ScreenSaver-11.0.dist-info/RECORD,,
pyobjc_framework_ScreenSaver-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_ScreenSaver-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_ScreenSaver-11.0.dist-info/top_level.txt,sha256=Y5Vby1Q1vDkNln9V0BeT-f_-2gcPWeq62czo4wLLzaY,12
