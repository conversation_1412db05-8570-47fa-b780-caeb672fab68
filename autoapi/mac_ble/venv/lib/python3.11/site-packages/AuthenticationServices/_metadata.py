# This file is generated by objective.metadata
#
# Last update: Sun Nov 17 11:38:19 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$ASAuthorizationAppleIDProviderCredentialRevokedNotification$ASAuthorizationCustomMethodOther$ASAuthorizationCustomMethodRestorePurchase$ASAuthorizationCustomMethodVideoSubscriberAccount$ASAuthorizationErrorDomain$ASAuthorizationOperationImplicit$ASAuthorizationOperationLogin$ASAuthorizationOperationLogout$ASAuthorizationOperationRefresh$ASAuthorizationProviderAuthorizationOperationConfigurationRemoved$ASAuthorizationProviderAuthorizationOperationDirectRequest$ASAuthorizationProviderExtensionEncryptionAlgorithmECDHE_A256GCM$ASAuthorizationProviderExtensionEncryptionAlgorithmHPKE_Curve25519_SHA256_ChachaPoly$ASAuthorizationProviderExtensionEncryptionAlgorithmHPKE_P256_SHA256_AES_GCM_256$ASAuthorizationProviderExtensionEncryptionAlgorithmHPKE_P384_SHA384_AES_GCM_256$ASAuthorizationProviderExtensionSigningAlgorithmES256$ASAuthorizationProviderExtensionSigningAlgorithmES384$ASAuthorizationProviderExtensionSigningAlgorithmEd25519$ASAuthorizationPublicKeyCredentialAttestationKindDirect$ASAuthorizationPublicKeyCredentialAttestationKindEnterprise$ASAuthorizationPublicKeyCredentialAttestationKindIndirect$ASAuthorizationPublicKeyCredentialAttestationKindNone$ASAuthorizationPublicKeyCredentialResidentKeyPreferenceDiscouraged$ASAuthorizationPublicKeyCredentialResidentKeyPreferencePreferred$ASAuthorizationPublicKeyCredentialResidentKeyPreferenceRequired$ASAuthorizationPublicKeyCredentialUserVerificationPreferenceDiscouraged$ASAuthorizationPublicKeyCredentialUserVerificationPreferencePreferred$ASAuthorizationPublicKeyCredentialUserVerificationPreferenceRequired$ASAuthorizationScopeEmail$ASAuthorizationScopeFullName$ASAuthorizationSecurityKeyPublicKeyCredentialDescriptorTransportBluetooth$ASAuthorizationSecurityKeyPublicKeyCredentialDescriptorTransportNFC$ASAuthorizationSecurityKeyPublicKeyCredentialDescriptorTransportUSB$ASCredentialIdentityStoreErrorDomain$ASExtensionErrorDomain$ASExtensionLocalizedFailureReasonErrorKey$ASWebAuthenticationSessionErrorDomain$"""
enums = """$ASAuthorizationAppleIDButtonStyleBlack@2$ASAuthorizationAppleIDButtonStyleWhite@0$ASAuthorizationAppleIDButtonStyleWhiteOutline@1$ASAuthorizationAppleIDButtonTypeContinue@1$ASAuthorizationAppleIDButtonTypeDefault@0$ASAuthorizationAppleIDButtonTypeSignIn@0$ASAuthorizationAppleIDButtonTypeSignUp@2$ASAuthorizationAppleIDProviderCredentialAuthorized@1$ASAuthorizationAppleIDProviderCredentialNotFound@2$ASAuthorizationAppleIDProviderCredentialRevoked@0$ASAuthorizationAppleIDProviderCredentialTransferred@3$ASAuthorizationControllerRequestOptionPreferImmediatelyAvailableCredentials@1$ASAuthorizationErrorCanceled@1001$ASAuthorizationErrorCredentialExport@1008$ASAuthorizationErrorCredentialImport@1007$ASAuthorizationErrorFailed@1004$ASAuthorizationErrorInvalidResponse@1002$ASAuthorizationErrorMatchedExcludedCredential@1006$ASAuthorizationErrorNotHandled@1003$ASAuthorizationErrorNotInteractive@1005$ASAuthorizationErrorUnknown@1000$ASAuthorizationPlatformPublicKeyCredentialRegistrationRequestStyleConditional@1$ASAuthorizationPlatformPublicKeyCredentialRegistrationRequestStyleStandard@0$ASAuthorizationProviderExtensionAuthenticationMethodPassword@1$ASAuthorizationProviderExtensionAuthenticationMethodSmartCard@3$ASAuthorizationProviderExtensionAuthenticationMethodUserSecureEnclaveKey@2$ASAuthorizationProviderExtensionFederationTypeDynamicWSTrust@2$ASAuthorizationProviderExtensionFederationTypeNone@0$ASAuthorizationProviderExtensionFederationTypeWSTrust@1$ASAuthorizationProviderExtensionKeyTypeCurrentDeviceEncryption@11$ASAuthorizationProviderExtensionKeyTypeCurrentDeviceSigning@10$ASAuthorizationProviderExtensionKeyTypeSharedDeviceEncryption@5$ASAuthorizationProviderExtensionKeyTypeSharedDeviceSigning@4$ASAuthorizationProviderExtensionKeyTypeUserDeviceEncryption@2$ASAuthorizationProviderExtensionKeyTypeUserDeviceSigning@1$ASAuthorizationProviderExtensionKeyTypeUserSecureEnclaveKey@3$ASAuthorizationProviderExtensionKeyTypeUserSmartCard@20$ASAuthorizationProviderExtensionPlatformSSOProtocolVersion1_0@0$ASAuthorizationProviderExtensionPlatformSSOProtocolVersion2_0@1$ASAuthorizationProviderExtensionRegistrationResultFailed@1$ASAuthorizationProviderExtensionRegistrationResultFailedNoRetry@3$ASAuthorizationProviderExtensionRegistrationResultSuccess@0$ASAuthorizationProviderExtensionRegistrationResultUserInterfaceRequired@2$ASAuthorizationProviderExtensionRequestOptionsNone@0$ASAuthorizationProviderExtensionRequestOptionsRegistrationDeviceKeyMigration@8$ASAuthorizationProviderExtensionRequestOptionsRegistrationRepair@2$ASAuthorizationProviderExtensionRequestOptionsRegistrationSharedDeviceKeys@4$ASAuthorizationProviderExtensionRequestOptionsStrongerKeyAvailable@16$ASAuthorizationProviderExtensionRequestOptionsUserInteractionEnabled@1$ASAuthorizationProviderExtensionRequestOptionsUserKeyInvalid@32$ASAuthorizationProviderExtensionSupportedGrantTypesJWTBearer@2$ASAuthorizationProviderExtensionSupportedGrantTypesNone@0$ASAuthorizationProviderExtensionSupportedGrantTypesPassword@1$ASAuthorizationProviderExtensionSupportedGrantTypesSAML1_1@4$ASAuthorizationProviderExtensionSupportedGrantTypesSAML2_0@8$ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicyNone@0$ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicyPasswordFallback@8$ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicyReuseDuringUnlock@4$ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicyTouchIDOrWatchAny@2$ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicyTouchIDOrWatchCurrentSet@1$ASAuthorizationPublicKeyCredentialAttachmentCrossPlatform@1$ASAuthorizationPublicKeyCredentialAttachmentPlatform@0$ASAuthorizationPublicKeyCredentialLargeBlobAssertionOperationRead@0$ASAuthorizationPublicKeyCredentialLargeBlobAssertionOperationWrite@1$ASAuthorizationPublicKeyCredentialLargeBlobSupportRequirementPreferred@1$ASAuthorizationPublicKeyCredentialLargeBlobSupportRequirementRequired@0$ASAuthorizationWebBrowserPublicKeyCredentialManagerAuthorizationStateAuthorized@0$ASAuthorizationWebBrowserPublicKeyCredentialManagerAuthorizationStateDenied@1$ASAuthorizationWebBrowserPublicKeyCredentialManagerAuthorizationStateNotDetermined@2$ASCOSEAlgorithmIdentifierES256@-7$ASCOSEEllipticCurveIdentifierP256@1$ASCredentialIdentityStoreErrorCodeInternalError@0$ASCredentialIdentityStoreErrorCodeStoreBusy@2$ASCredentialIdentityStoreErrorCodeStoreDisabled@1$ASCredentialIdentityTypesAll@0$ASCredentialIdentityTypesOneTimeCode@4$ASCredentialIdentityTypesPasskey@2$ASCredentialIdentityTypesPassword@1$ASCredentialRequestTypeOneTimeCode@3$ASCredentialRequestTypePasskeyAssertion@1$ASCredentialRequestTypePasskeyRegistration@2$ASCredentialRequestTypePassword@0$ASCredentialServiceIdentifierTypeDomain@0$ASCredentialServiceIdentifierTypeURL@1$ASExtensionErrorCodeCredentialIdentityNotFound@101$ASExtensionErrorCodeFailed@0$ASExtensionErrorCodeMatchedExcludedCredential@102$ASExtensionErrorCodeUserCanceled@1$ASExtensionErrorCodeUserInteractionRequired@100$ASPublicKeyCredentialClientDataCrossOriginValueCrossOrigin@1$ASPublicKeyCredentialClientDataCrossOriginValueNotSet@0$ASPublicKeyCredentialClientDataCrossOriginValueSameOriginWithAncestors@2$ASUserAgeRangeChild@1$ASUserAgeRangeNotChild@2$ASUserAgeRangeUnknown@0$ASUserDetectionStatusLikelyReal@2$ASUserDetectionStatusUnknown@1$ASUserDetectionStatusUnsupported@0$ASWebAuthenticationSessionErrorCodeCanceledLogin@1$ASWebAuthenticationSessionErrorCodePresentationContextInvalid@3$ASWebAuthenticationSessionErrorCodePresentationContextNotProvided@2$"""
misc.update(
    {
        "ASAuthorizationProviderExtensionAuthenticationMethod": NewType(
            "ASAuthorizationProviderExtensionAuthenticationMethod", int
        ),
        "ASAuthorizationProviderExtensionPlatformSSOProtocolVersion": NewType(
            "ASAuthorizationProviderExtensionPlatformSSOProtocolVersion", int
        ),
        "ASAuthorizationAppleIDProviderCredentialState": NewType(
            "ASAuthorizationAppleIDProviderCredentialState", int
        ),
        "ASAuthorizationPublicKeyCredentialLargeBlobSupportRequirement": NewType(
            "ASAuthorizationPublicKeyCredentialLargeBlobSupportRequirement", int
        ),
        "ASUserAgeRange": NewType("ASUserAgeRange", int),
        "ASCredentialRequestType": NewType("ASCredentialRequestType", int),
        "ASCredentialServiceIdentifierType": NewType(
            "ASCredentialServiceIdentifierType", int
        ),
        "ASCredentialIdentityTypes": NewType("ASCredentialIdentityTypes", int),
        "ASCredentialIdentityStoreErrorCode": NewType(
            "ASCredentialIdentityStoreErrorCode", int
        ),
        "ASAuthorizationError": NewType("ASAuthorizationError", int),
        "ASAuthorizationAppleIDButtonType": NewType(
            "ASAuthorizationAppleIDButtonType", int
        ),
        "ASAuthorizationProviderExtensionKeyType": NewType(
            "ASAuthorizationProviderExtensionKeyType", int
        ),
        "ASAuthorizationPublicKeyCredentialAttachment": NewType(
            "ASAuthorizationPublicKeyCredentialAttachment", int
        ),
        "ASExtensionErrorCode": NewType("ASExtensionErrorCode", int),
        "ASAuthorizationPlatformPublicKeyCredentialRegistrationRequestStyle": NewType(
            "ASAuthorizationPlatformPublicKeyCredentialRegistrationRequestStyle", int
        ),
        "ASAuthorizationProviderExtensionFederationType": NewType(
            "ASAuthorizationProviderExtensionFederationType", int
        ),
        "ASPublicKeyCredentialClientDataCrossOriginValue": NewType(
            "ASPublicKeyCredentialClientDataCrossOriginValue", int
        ),
        "ASAuthorizationProviderExtensionSupportedGrantTypes": NewType(
            "ASAuthorizationProviderExtensionSupportedGrantTypes", int
        ),
        "ASAuthorizationControllerRequestOptions": NewType(
            "ASAuthorizationControllerRequestOptions", int
        ),
        "ASAuthorizationPublicKeyCredentialLargeBlobAssertionOperation": NewType(
            "ASAuthorizationPublicKeyCredentialLargeBlobAssertionOperation", int
        ),
        "ASAuthorizationProviderExtensionRegistrationResult": NewType(
            "ASAuthorizationProviderExtensionRegistrationResult", int
        ),
        "ASUserDetectionStatus": NewType("ASUserDetectionStatus", int),
        "ASAuthorizationProviderExtensionRequestOptions": NewType(
            "ASAuthorizationProviderExtensionRequestOptions", int
        ),
        "ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicy": NewType(
            "ASAuthorizationProviderExtensionUserSecureEnclaveKeyBiometricPolicy", int
        ),
        "ASAuthorizationWebBrowserPublicKeyCredentialManagerAuthorizationState": NewType(
            "ASAuthorizationWebBrowserPublicKeyCredentialManagerAuthorizationState", int
        ),
        "ASAuthorizationAppleIDButtonStyle": NewType(
            "ASAuthorizationAppleIDButtonStyle", int
        ),
        "ASWebAuthenticationSessionErrorCode": NewType(
            "ASWebAuthenticationSessionErrorCode", int
        ),
    }
)
misc.update(
    {
        "ASAuthorizationPublicKeyCredentialUserVerificationPreference": NewType(
            "ASAuthorizationPublicKeyCredentialUserVerificationPreference", str
        ),
        "ASAuthorizationProviderAuthorizationOperation": NewType(
            "ASAuthorizationProviderAuthorizationOperation", str
        ),
        "ASAuthorizationSecurityKeyPublicKeyCredentialDescriptorTransport": NewType(
            "ASAuthorizationSecurityKeyPublicKeyCredentialDescriptorTransport", str
        ),
        "ASAuthorizationProviderExtensionEncryptionAlgorithm": NewType(
            "ASAuthorizationProviderExtensionEncryptionAlgorithm",
            objc.lookUpClass("NSNumber"),
        ),
        "ASAuthorizationScope": NewType("ASAuthorizationScope", str),
        "ASAuthorizationPublicKeyCredentialResidentKeyPreference": NewType(
            "ASAuthorizationPublicKeyCredentialResidentKeyPreference", str
        ),
        "ASCOSEEllipticCurveIdentifier": NewType("ASCOSEEllipticCurveIdentifier", int),
        "ASAuthorizationProviderExtensionSigningAlgorithm": NewType(
            "ASAuthorizationProviderExtensionSigningAlgorithm",
            objc.lookUpClass("NSNumber"),
        ),
        "ASAuthorizationPublicKeyCredentialAttestationKind": NewType(
            "ASAuthorizationPublicKeyCredentialAttestationKind", str
        ),
        "ASAuthorizationProviderExtensionFederationType": NewType(
            "ASAuthorizationProviderExtensionFederationType", int
        ),
        "ASAuthorizationOpenIDOperation": NewType(
            "ASAuthorizationOpenIDOperation", str
        ),
        "ASAuthorizationCustomMethod": NewType("ASAuthorizationCustomMethod", str),
        "ASCOSEAlgorithmIdentifier": NewType("ASCOSEAlgorithmIdentifier", int),
    }
)
misc.update({})
functions = {
    "ASAuthorizationAllSupportedPublicKeyCredentialDescriptorTransports": (b"@",)
}
aliases = {
    "AS_SWIFT_SENDABLE": "NS_SWIFT_SENDABLE",
    "AS_API_AVAILABLE": "API_AVAILABLE",
    "AS_HEADER_AUDIT_END": "NS_HEADER_AUDIT_END",
    "ASAuthorizationAppleIDButtonTypeDefault": "ASAuthorizationAppleIDButtonTypeSignIn",
    "AS_HEADER_AUDIT_BEGIN": "NS_HEADER_AUDIT_BEGIN",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"ASAccountAuthenticationModificationExtensionContext",
        b"getSignInWithAppleUpgradeAuthorizationWithState:nonce:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationAppleIDProvider",
        b"getCredentialStateForUserID:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationProviderExtensionAuthorizationRequest",
        b"isCallerManaged",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationProviderExtensionAuthorizationRequest",
        b"isUserInterfaceEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationProviderExtensionAuthorizationRequest",
        b"presentAuthorizationViewControllerWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"configurationWithOpenIDConfigurationURL:clientID:issuer:completion:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"includePreviousRefreshTokenInLoginRequest",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomAssertionRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomAssertionRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomKeyExchangeRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomKeyExchangeRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomKeyRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomKeyRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomLoginRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomLoginRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomRefreshRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setCustomRefreshRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginConfiguration",
        b"setIncludePreviousRefreshTokenInLoginRequest:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"copyIdentityForKeyType:",
        {"retval": {"already_cfretained": True}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"copyKeyForKeyType:",
        {"retval": {"already_cfretained": True}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"isDeviceRegistered",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"isUserRegistered",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"presentRegistrationViewControllerWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"saveLoginConfiguration:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"saveUserLoginConfiguration:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionLoginManager",
        b"userNeedsReauthenticationWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationProviderExtensionUserLoginConfiguration",
        b"setCustomAssertionRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionUserLoginConfiguration",
        b"setCustomAssertionRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionUserLoginConfiguration",
        b"setCustomLoginRequestBodyClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationProviderExtensionUserLoginConfiguration",
        b"setCustomLoginRequestHeaderClaims:returningError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"ASAuthorizationPublicKeyCredentialLargeBlobAssertionOutput",
        b"didWrite",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationPublicKeyCredentialLargeBlobRegistrationOutput",
        b"isSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationPublicKeyCredentialPRFRegistrationInput",
        b"shouldCheckForSupport",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationPublicKeyCredentialPRFRegistrationOutput",
        b"isSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationSecurityKeyPublicKeyCredentialAssertion",
        b"appID",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationSingleSignOnProvider",
        b"canPerformAuthorization",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationSingleSignOnRequest",
        b"isUserInterfaceEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASAuthorizationSingleSignOnRequest",
        b"setUserInterfaceEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"ASAuthorizationWebBrowserPublicKeyCredentialManager",
        b"platformCredentialsForRelyingParty:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASAuthorizationWebBrowserPublicKeyCredentialManager",
        b"requestAuthorizationForPublicKeyCredentials:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"getCredentialIdentitiesForService:credentialIdentityTypes:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"getCredentialIdentityStoreStateWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"removeAllCredentialIdentitiesWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"removeCredentialIdentities:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"removeCredentialIdentityEntries:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"replaceCredentialIdentitiesWithIdentities:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"replaceCredentialIdentityEntries:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"saveCredentialIdentities:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialIdentityStore",
        b"saveCredentialIdentityEntries:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"ASCredentialIdentityStoreState", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"ASCredentialIdentityStoreState",
        b"supportsIncrementalUpdates",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASCredentialProviderExtensionContext",
        b"completeAssertionRequestWithSelectedPasskeyCredential:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialProviderExtensionContext",
        b"completeOneTimeCodeRequestWithSelectedCredential:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialProviderExtensionContext",
        b"completeRegistrationRequestWithSelectedPasskeyCredential:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialProviderExtensionContext",
        b"completeRequestReturningItems:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"ASCredentialProviderExtensionContext",
        b"completeRequestWithSelectedCredential:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"ASSettingsHelper",
        b"openCredentialProviderAppSettingsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASSettingsHelper",
        b"openVerificationCodeAppSettingsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"ASSettingsHelper",
        b"requestToTurnOnCredentialProviderExtensionWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(b"ASWebAuthenticationSession", b"canStart", {"retval": {"type": "Z"}})
    r(
        b"ASWebAuthenticationSession",
        b"initWithURL:callback:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASWebAuthenticationSession",
        b"initWithURL:callbackURLScheme:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"ASWebAuthenticationSession",
        b"prefersEphemeralWebBrowserSession",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASWebAuthenticationSession",
        b"setPrefersEphemeralWebBrowserSession:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"ASWebAuthenticationSession", b"start", {"retval": {"type": b"Z"}})
    r(b"ASWebAuthenticationSessionCallback", b"matchesURL:", {"retval": {"type": b"Z"}})
    r(
        b"ASWebAuthenticationSessionRequest",
        b"shouldUseEphemeralSession",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"ASWebAuthenticationSessionWebBrowserSessionManager",
        b"wasLaunchedByAuthenticationServices",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"accountAuthenticationModificationController:didFailRequest:withError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accountAuthenticationModificationController:didSuccessfullyCompleteRequest:withUserInfo:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"allowedCredentials", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"attestationPreference",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"authenticatedContext",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"authenticationSessionRequest:didCancelWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"authenticationSessionRequest:didCompleteWithCallbackURL:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"authorizationController:didCompleteWithAuthorization:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"authorizationController:didCompleteWithCustomMethod:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"authorizationController:didCompleteWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"beginAuthorizationWithRequest:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"beginDeviceRegistrationUsingLoginManager:options:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"beginHandlingWebAuthenticationSessionRequest:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"beginUserRegistrationUsingLoginManager:userName:authenticationMethod:options:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"Q"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"cancelAuthorizationWithRequest:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"cancelWebAuthenticationSessionRequest:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"challenge", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"clientData", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"createCredentialAssertionRequestWithClientData:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"createCredentialRegistrationRequestWithClientData:displayName:name:userID:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"createCredentialRegistrationRequestWithClientData:name:userID:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"createCredentialRegistrationRequestWithClientData:name:userID:requestStyle:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"q"},
            },
        },
    )
    r(b"NSObject", b"credentialID", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"credentialIdentity", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"displayName", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"excludedCredentials", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"keyWillRotateForKeyType:newKey:loginManager:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"q"},
                3: {"type": b"^{__SecKey=}"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"name", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"presentationAnchorForAccountAuthenticationModificationController:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentationAnchorForAuthorizationController:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentationAnchorForWebAuthenticationSession:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"protocolVersion", {"required": False, "retval": {"type": b"q"}})
    r(b"NSObject", b"rank", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"rawAttestationObject",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"rawAuthenticatorData",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"rawClientDataJSON", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"recordIdentifier", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"registrationDidCancel",
        {"required": False, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"registrationDidComplete",
        {"required": False, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"relyingPartyIdentifier",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"serviceIdentifier", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"setAllowedCredentials:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setAttestationPreference:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setAuthenticatedContext:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setChallenge:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setCredentialID:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setDisplayName:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setExcludedCredentials:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setName:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setRank:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"setRelyingPartyIdentifier:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setShouldShowHybridTransport:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setUserID:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setUserVerificationPreference:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"shouldShowHybridTransport",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"signature", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"supportedDeviceEncryptionAlgorithms",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"supportedDeviceSigningAlgorithms",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"supportedGrantTypes",
        {"required": False, "retval": {"type": b"q"}},
    )
    r(
        b"NSObject",
        b"supportedUserSecureEnclaveKeySigningAlgorithms",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"type", {"required": True, "retval": {"type": b"q"}})
    r(b"NSObject", b"user", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"userID", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"userVerificationPreference",
        {"required": True, "retval": {"type": b"@"}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "ASAccountAuthenticationModificationReplacePasswordWithSignInWithAppleRequest",
    b"initWithUser:serviceIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "ASAccountAuthenticationModificationReplacePasswordWithSignInWithAppleRequest",
    b"initWithUser:serviceIdentifier:userInfo:",
)
objc.registerNewKeywordsFromSelector(
    "ASAccountAuthenticationModificationUpgradePasswordToStrongPasswordRequest",
    b"initWithUser:serviceIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "ASAccountAuthenticationModificationUpgradePasswordToStrongPasswordRequest",
    b"initWithUser:serviceIdentifier:userInfo:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationAppleIDButton",
    b"initWithAuthorizationButtonType:authorizationButtonStyle:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationController", b"initWithAuthorizationRequests:"
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPlatformPublicKeyCredentialDescriptor", b"initWithCredentialID:"
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPlatformPublicKeyCredentialProvider",
    b"initWithRelyingPartyIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationProviderExtensionAuthorizationResult",
    b"initWithHTTPAuthorizationHeaders:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationProviderExtensionAuthorizationResult",
    b"initWithHTTPResponse:httpBody:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationProviderExtensionLoginConfiguration",
    b"initWithClientID:issuer:tokenEndpointURL:jwksEndpointURL:audience:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationProviderExtensionUserLoginConfiguration", b"initWithLoginUserName:"
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPublicKeyCredentialLargeBlobAssertionInput", b"initWithOperation:"
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPublicKeyCredentialLargeBlobRegistrationInput",
    b"initWithSupportRequirement:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPublicKeyCredentialPRFAssertionInput",
    b"initWithInputValues:perCredentialInputValues:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPublicKeyCredentialPRFAssertionInputValues",
    b"initWithSaltInput1:saltInput2:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPublicKeyCredentialPRFRegistrationInput", b"initWithInputValues:"
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationPublicKeyCredentialParameters", b"initWithAlgorithm:"
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationSecurityKeyPublicKeyCredentialDescriptor",
    b"initWithCredentialID:transports:",
)
objc.registerNewKeywordsFromSelector(
    "ASAuthorizationSecurityKeyPublicKeyCredentialProvider",
    b"initWithRelyingPartyIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "ASCredentialServiceIdentifier", b"initWithIdentifier:type:"
)
objc.registerNewKeywordsFromSelector("ASOneTimeCodeCredential", b"initWithCode:")
objc.registerNewKeywordsFromSelector(
    "ASOneTimeCodeCredentialIdentity",
    b"initWithServiceIdentifier:label:recordIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "ASOneTimeCodeCredentialRequest", b"initWithCredentialIdentity:"
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyAssertionCredential",
    b"initWithUserHandle:relyingParty:signature:clientDataHash:authenticatorData:credentialID:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyAssertionCredential",
    b"initWithUserHandle:relyingParty:signature:clientDataHash:authenticatorData:credentialID:extensionOutput:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyAssertionCredentialExtensionOutput", b"initWithLargeBlobOutput:"
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyCredentialIdentity",
    b"initWithRelyingPartyIdentifier:userName:credentialID:userHandle:recordIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyCredentialRequest",
    b"initWithCredentialIdentity:clientDataHash:userVerificationPreference:supportedAlgorithms:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyCredentialRequest",
    b"initWithCredentialIdentity:clientDataHash:userVerificationPreference:supportedAlgorithms:assertionExtensionInput:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyCredentialRequest",
    b"initWithCredentialIdentity:clientDataHash:userVerificationPreference:supportedAlgorithms:registrationExtensionInput:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyRegistrationCredential",
    b"initWithRelyingParty:clientDataHash:credentialID:attestationObject:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyRegistrationCredential",
    b"initWithRelyingParty:clientDataHash:credentialID:attestationObject:extensionOutput:",
)
objc.registerNewKeywordsFromSelector(
    "ASPasskeyRegistrationCredentialExtensionOutput", b"initWithLargeBlobOutput:"
)
objc.registerNewKeywordsFromSelector("ASPasswordCredential", b"initWithUser:password:")
objc.registerNewKeywordsFromSelector(
    "ASPasswordCredentialIdentity", b"initWithServiceIdentifier:user:recordIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "ASPasswordCredentialRequest", b"initWithCredentialIdentity:"
)
objc.registerNewKeywordsFromSelector(
    "ASPublicKeyCredentialClientData", b"initWithChallenge:origin:"
)
objc.registerNewKeywordsFromSelector(
    "ASWebAuthenticationSession", b"initWithURL:callback:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "ASWebAuthenticationSession", b"initWithURL:callbackURLScheme:completionHandler:"
)
expressions = {}

# END OF FILE
