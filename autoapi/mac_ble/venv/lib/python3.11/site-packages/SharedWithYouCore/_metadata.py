# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:20:11 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = (
    """$SharedWithYouCoreVersionNumber@d$UTCollaborationOptionsTypeIdentifier$"""
)
enums = """$$"""
misc.update({})
misc.update(
    {
        "SWCollaborationIdentifier": NewType("SWCollaborationIdentifier", str),
        "SWLocalCollaborationIdentifier": NewType(
            "SWLocalCollaborationIdentifier", str
        ),
    }
)
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSObject",
        b"collaborationCoordinator:handleStartCollaborationAction:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"collaborationCoordinator:handleUpdateCollaborationParticipantsAction:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"SWAction", b"isComplete", {"retval": {"type": b"Z"}})
    r(b"SWCollaborationOption", b"isSelected", {"retval": {"type": b"Z"}})
    r(b"SWCollaborationOption", b"setSelected:", {"arguments": {2: {"type": b"Z"}}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "SWCollaborationMetadata", b"initWithCollaborationIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "SWCollaborationMetadata", b"initWithLocalIdentifier:"
)
objc.registerNewKeywordsFromSelector("SWCollaborationOption", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "SWCollaborationOption", b"initWithTitle:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "SWCollaborationOptionsGroup", b"initWithIdentifier:options:"
)
objc.registerNewKeywordsFromSelector("SWCollaborationShareOptions", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "SWCollaborationShareOptions", b"initWithOptionsGroups:"
)
objc.registerNewKeywordsFromSelector(
    "SWCollaborationShareOptions", b"initWithOptionsGroups:summary:"
)
objc.registerNewKeywordsFromSelector(
    "SWPerson", b"initWithHandle:identity:displayName:thumbnailImageData:"
)
objc.registerNewKeywordsFromSelector("SWPersonIdentity", b"initWithRootHash:")
objc.registerNewKeywordsFromSelector(
    "SWSignedPersonIdentityProof", b"initWithPersonIdentityProof:signatureData:"
)
expressions = {}

# END OF FILE
