CloudKit/__init__.py,sha256=aOJWU54hrBjEXQgm9BhePUF43pKNcs54CXYwM6CccoY,6271
CloudKit/__pycache__/__init__.cpython-311.pyc,,
CloudKit/__pycache__/_metadata.cpython-311.pyc,,
CloudKit/_metadata.py,sha256=veWRh3498dnEKl3ia-XVgL-z7otaG5qbD07O7lIKXf4,96085
pyobjc_framework_CloudKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_CloudKit-11.0.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_CloudKit-11.0.dist-info/METADATA,sha256=-RlQBK7J3iFrCHLGON9aVCB_CuB2g7oEN3td8xOJ8ic,2654
pyobjc_framework_CloudKit-11.0.dist-info/RECORD,,
pyobjc_framework_CloudKit-11.0.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
pyobjc_framework_CloudKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_CloudKit-11.0.dist-info/top_level.txt,sha256=z7XD0iXYs36v7_IFOgKCe9zhkmRayocyyxmErwD6ZwQ,9
