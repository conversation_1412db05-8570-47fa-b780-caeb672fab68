StoreKit/_StoreKit.cpython-311-darwin.so,sha256=DZtJizCxT-L7THDDNeHKfHuKQkESmJv3d3g7LrZ5KTk,70112
StoreKit/__init__.py,sha256=mOtZrbXbQ8IfmSMrjMyhgg4Oc52_ma44B2-xgszm8Fk,1666
StoreKit/__pycache__/__init__.cpython-311.pyc,,
StoreKit/__pycache__/_metadata.cpython-311.pyc,,
StoreKit/_metadata.py,sha256=0oFE3NwW-DJ9yVEBH8boBafa2LISGVq2w2CctUxrs9M,21269
pyobjc_framework_StoreKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_StoreKit-11.0.dist-info/METADATA,sha256=KgK3ifCDIsK2ETMq9R6h02V4-fxKoXkfR7__LVpLrJs,2252
pyobjc_framework_StoreKit-11.0.dist-info/RECORD,,
pyobjc_framework_StoreKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_StoreKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_StoreKit-11.0.dist-info/top_level.txt,sha256=4smnkO902dwBWt35yDEWhiG0tVsZ-Eot9sm1iF4znlI,9
