PassKit/_PassKit.cpython-311-darwin.so,sha256=nV0jm-SNgPU13WEi-tOOy34_wm371uDumUSB-GLq-JQ,85360
PassKit/__init__.py,sha256=oHYMdoIKNeOKCBCPEOkkl2yG3XTib4n2WzeuEfDdSUE,2446
PassKit/__pycache__/__init__.cpython-311.pyc,,
PassKit/__pycache__/_metadata.cpython-311.pyc,,
PassKit/_metadata.py,sha256=tfIIfPg8E2Ifv9McrmQtLI_WgIny3A9Ie_6Ce1xKAyQ,47882
pyobjc_framework_PassKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_PassKit-11.0.dist-info/METADATA,sha256=aglp6Bdm6mmFwp81k4CRxSBIucfgBxtsUSs9mgpgV1s,2235
pyobjc_framework_PassKit-11.0.dist-info/RECORD,,
pyobjc_framework_PassKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_PassKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_PassKit-11.0.dist-info/top_level.txt,sha256=8mHCaP24wRxnuaunNngG0p8QvlIicCTWkynfMj3B4U4,8
