OSLog/_OSLog.cpython-311-darwin.so,sha256=mCce7xsgSGylx7vrqQJXat9gdnhB9HfTkYTb0eS9LxI,68000
OSLog/__init__.py,sha256=xhsKqyXpDQ4ua85WjssZeCTG6nlbIIWM0LEe-IbTeSo,971
OSLog/__pycache__/__init__.cpython-311.pyc,,
OSLog/__pycache__/_metadata.cpython-311.pyc,,
OSLog/_metadata.py,sha256=5HaMuCKbVQ4ohqJ7RhOJVt3hbLo_voNDbww548UeTbw,3483
pyobjc_framework_OSLog-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_OSLog-11.0.dist-info/METADATA,sha256=v98djdJf4PO0Zt-qmZ45PM5QI7qqt-eJ_NzY5Vvkt5A,2326
pyobjc_framework_OSLog-11.0.dist-info/RECORD,,
pyobjc_framework_OSLog-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_OSLog-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_OSLog-11.0.dist-info/top_level.txt,sha256=suGr-k3JNTOBjhpZrvQq0jnIr9Xgw6dZ5xnvN7HXkj8,6
