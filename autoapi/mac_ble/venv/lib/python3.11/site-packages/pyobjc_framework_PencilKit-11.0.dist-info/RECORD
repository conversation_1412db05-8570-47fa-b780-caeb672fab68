PencilKit/__init__.py,sha256=3S2xYLkNG-uwzwPyVcWmJl2AtUDCEbQRtWmQI_M7reg,1012
PencilKit/__pycache__/__init__.cpython-311.pyc,,
PencilKit/__pycache__/_metadata.cpython-311.pyc,,
PencilKit/_metadata.py,sha256=lJVi8l0vlVy7-E5Ru6RrYPYnvbhtNU92BKJPMEEVWtk,4051
pyobjc_framework_PencilKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_PencilKit-11.0.dist-info/METADATA,sha256=kxiS6sN4-O-HpzseOlYY5GhSbJ14rf1bb968JIrc5a0,2503
pyobjc_framework_PencilKit-11.0.dist-info/RECORD,,
pyobjc_framework_PencilKit-11.0.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
pyobjc_framework_PencilKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_PencilKit-11.0.dist-info/top_level.txt,sha256=q7VRaK5IOsvo-AApq9Lls31C6e1lrPmoOkukANSsX2k,10
