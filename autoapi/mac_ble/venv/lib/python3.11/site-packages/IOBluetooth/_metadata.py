# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:23:50 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "BluetoothHCIEventLEMetaResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLEMetaResults",
            b"{BluetoothHCIEventLEMetaResults=C[255C]}",
            ["length", "data"],
        ),
        "IOBluetoothL2CAPChannelDataBlock": objc.createStructType(
            "IOBluetooth.IOBluetoothL2CAPChannelDataBlock",
            b"{IOBluetoothL2CAPChannelDataBlock=^vQ}",
            ["dataPtr", "dataSize"],
        ),
        "BluetoothHCIInquiryResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIInquiryResults",
            b"{BluetoothHCIInquiryResults=[50{BluetoothHCIInquiryResult={BluetoothDeviceAddress=[6C]}CCCIS}]I}",
            ["results", "count"],
        ),
        "BluetoothHCILinkQualityInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCILinkQualityInfo",
            b"{BluetoothHCILinkQualityInfo=SC}",
            ["handle", "qualityValue"],
        ),
        "BluetoothHCIReadLMPHandleResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIReadLMPHandleResults",
            b"{BluetoothHCIReadLMPHandleResults=SCI}",
            ["handle", "lmp_handle", "reserved"],
        ),
        "BluetoothHCIEventModeChangeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventModeChangeResults",
            b"{BluetoothHCIEventModeChangeResults=SCS}",
            ["connectionHandle", "mode", "modeInterval"],
        ),
        "BluetoothHCIEventDataBufferOverflowResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventDataBufferOverflowResults",
            b"{BluetoothHCIEventDataBufferOverflowResults=C}",
            ["linkType"],
        ),
        "BluetoothDeviceAddress": objc.createStructType(
            "IOBluetooth.BluetoothDeviceAddress",
            b"{BluetoothDeviceAddress=[6C]}",
            ["data"],
        ),
        "BluetoothIRK": objc.createStructType(
            "IOBluetooth.BluetoothIRK", b"{BluetoothIRK=[16C]}", ["data"]
        ),
        "BluetoothL2CAPRetransmissionAndFlowControlOptions": objc.createStructType(
            "IOBluetooth.BluetoothL2CAPRetransmissionAndFlowControlOptions",
            b"{BluetoothL2CAPRetransmissionAndFlowControlOptions=CCCSSS}",
            [
                "flags",
                "txWindowSize",
                "maxTransmit",
                "retransmissionTimeout",
                "monitorTimeout",
                "maxPDUPayloadSize",
            ],
        ),
        "BluetoothHCIEventHardwareErrorResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventHardwareErrorResults",
            b"{BluetoothHCIEventHardwareErrorResults=C}",
            ["error"],
        ),
        "BluetoothPINCode": objc.createStructType(
            "IOBluetooth.BluetoothPINCode", b"{BluetoothPINCode=[16C]}", ["data"]
        ),
        "BluetoothHCIAcceptSynchronousConnectionRequestParams": objc.createStructType(
            "IOBluetooth.BluetoothHCIAcceptSynchronousConnectionRequestParams",
            b"{BluetoothHCIAcceptSynchronousConnectionRequestParams=IISSCS}",
            [
                "transmitBandwidth",
                "receiveBandwidth",
                "maxLatency",
                "contentFormat",
                "retransmissionEffort",
                "packetType",
            ],
        ),
        "BluetoothHCIEventSynchronousConnectionCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventSynchronousConnectionCompleteResults",
            b"{BluetoothHCIEventSynchronousConnectionCompleteResults=S{BluetoothDeviceAddress=[6C]}CCCSSC}",
            [
                "connectionHandle",
                "deviceAddress",
                "linkType",
                "transmissionInterval",
                "retransmissionWindow",
                "receivePacketLength",
                "transmitPacketLength",
                "airMode",
            ],
        ),
        "BluetoothRemoteHostSupportedFeaturesNotification": objc.createStructType(
            "IOBluetooth.BluetoothRemoteHostSupportedFeaturesNotification",
            b"{BluetoothRemoteHostSupportedFeaturesNotification={BluetoothDeviceAddress=[6C]}{BluetoothHCISupportedFeatures=[8C]}}",
            ["deviceAddress", "hostSupportedFeatures"],
        ),
        "BluetoothHCIExtendedFeaturesInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIExtendedFeaturesInfo",
            b"{BluetoothHCIExtendedFeaturesInfo=CC[8C]}",
            ["page", "maxPage", "data"],
        ),
        "OBEXErrorData": objc.createStructType(
            "IOBluetooth.OBEXErrorData",
            b"{OBEXErrorData=i^vQ}",
            ["error", "dataPtr", "dataLength"],
        ),
        "OBEXPutCommandResponseData": objc.createStructType(
            "IOBluetooth.OBEXPutCommandResponseData",
            b"{OBEXPutCommandResponseData=C^vQ}",
            ["serverResponseOpCode", "headerDataPtr", "headerDataLength"],
        ),
        "BluetoothHCIRequestCallbackInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIRequestCallbackInfo",
            b"{BluetoothHCIRequestCallbackInfo=QQQQQ}",
            [
                "userCallback",
                "userRefCon",
                "internalRefCon",
                "asyncIDRefCon",
                "reserved",
            ],
        ),
        "BluetoothHCIReadLocalOOBDataResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIReadLocalOOBDataResults",
            b"{BluetoothHCIReadLocalOOBDataResults={BluetoothHCISimplePairingOOBData=[16C]}{BluetoothHCISimplePairingOOBData=[16C]}}",
            ["hash", "randomizer"],
        ),
        "OBEXSetPathCommandData": objc.createStructType(
            "IOBluetooth.OBEXSetPathCommandData",
            b"{OBEXSetPathCommandData=^vQCC}",
            ["headerDataPtr", "headerDataLength", "flags", "constants"],
        ),
        "BluetoothHCIEventReadSupportedFeaturesResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventReadSupportedFeaturesResults",
            b"{BluetoothHCIEventReadSupportedFeaturesResults=S{BluetoothHCISupportedFeatures=[8C]}}",
            ["connectionHandle", "supportedFeatures"],
        ),
        "BluetoothHCIInquiryWithRSSIResult": objc.createStructType(
            "IOBluetooth.BluetoothHCIInquiryWithRSSIResult",
            b"{BluetoothHCIInquiryWithRSSIResult={BluetoothDeviceAddress=[6C]}CCISc}",
            [
                "deviceAddress",
                "pageScanRepetitionMode",
                "reserved",
                "classOfDevice",
                "clockOffset",
                "RSSIValue",
            ],
        ),
        "BluetoothHCISupportedCommands": objc.createStructType(
            "IOBluetooth.BluetoothHCISupportedCommands",
            b"{BluetoothHCISupportedCommands=[64C]}",
            ["data"],
        ),
        "BluetoothHCIAutomaticFlushTimeoutInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIAutomaticFlushTimeoutInfo",
            b"{BluetoothHCIAutomaticFlushTimeoutInfo=SS}",
            ["handle", "timeout"],
        ),
        "OBEXGetCommandResponseData": objc.createStructType(
            "IOBluetooth.OBEXGetCommandResponseData",
            b"{OBEXGetCommandResponseData=C^vQ}",
            ["serverResponseOpCode", "headerDataPtr", "headerDataLength"],
        ),
        "OBEXConnectCommandResponseData": objc.createStructType(
            "IOBluetooth.OBEXConnectCommandResponseData",
            b"{OBEXConnectCommandResponseData=C^vQSCC}",
            [
                "serverResponseOpCode",
                "headerDataPtr",
                "headerDataLength",
                "maxPacketSize",
                "version",
                "flags",
            ],
        ),
        "BluetoothHCIEventConnectionCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventConnectionCompleteResults",
            b"{BluetoothHCIEventConnectionCompleteResults=S{BluetoothDeviceAddress=[6C]}CC}",
            ["connectionHandle", "deviceAddress", "linkType", "encryptionMode"],
        ),
        "BluetoothHCILEBufferSize": objc.createStructType(
            "IOBluetooth.BluetoothHCILEBufferSize",
            b"{BluetoothHCILEBufferSize=SC}",
            ["ACLDataPacketLength", "totalNumACLDataPackets"],
        ),
        "OBEXPutCommandData": objc.createStructType(
            "IOBluetooth.OBEXPutCommandData",
            b"{OBEXPutCommandData=^vQQ}",
            ["headerDataPtr", "headerDataLength", "bodyDataLeftToSend"],
        ),
        "BluetoothHCIEventMaxSlotsChangeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventMaxSlotsChangeResults",
            b"{BluetoothHCIEventMaxSlotsChangeResults=SC}",
            ["connectionHandle", "maxSlots"],
        ),
        "OBEXAbortCommandData": objc.createStructType(
            "IOBluetooth.OBEXAbortCommandData",
            b"{OBEXAbortCommandData=^vQ}",
            ["headerDataPtr", "headerDataLength"],
        ),
        "BluetoothHCIEventMasterLinkKeyCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventMasterLinkKeyCompleteResults",
            b"{BluetoothHCIEventMasterLinkKeyCompleteResults=SC}",
            ["connectionHandle", "keyFlag"],
        ),
        "BluetoothHCIRoleInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIRoleInfo",
            b"{BluetoothHCIRoleInfo=CS}",
            ["role", "handle"],
        ),
        "OBEXDisconnectCommandData": objc.createStructType(
            "IOBluetooth.OBEXDisconnectCommandData",
            b"{OBEXDisconnectCommandData=^vQ}",
            ["headerDataPtr", "headerDataLength"],
        ),
        "BluetoothHCIEventLinkKeyNotificationResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLinkKeyNotificationResults",
            b"{BluetoothHCIEventLinkKeyNotificationResults={BluetoothDeviceAddress=[6C]}{BluetoothKey=[16C]}C}",
            ["deviceAddress", "linkKey", "keyType"],
        ),
        "BluetoothHCIScanActivity": objc.createStructType(
            "IOBluetooth.BluetoothHCIScanActivity",
            b"{BluetoothHCIScanActivity=SS}",
            ["scanInterval", "scanWindow"],
        ),
        "BluetoothHCIEventReadRemoteSupportedFeaturesResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventReadRemoteSupportedFeaturesResults",
            b"{BluetoothHCIEventReadRemoteSupportedFeaturesResults=CS{BluetoothHCISupportedFeatures=[8C]}}",
            ["error", "connectionHandle", "lmpFeatures"],
        ),
        "BluetoothKey": objc.createStructType(
            "IOBluetooth.BluetoothKey", b"{BluetoothKey=[16C]}", ["data"]
        ),
        "BluetoothHCIEventReadClockOffsetResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventReadClockOffsetResults",
            b"{BluetoothHCIEventReadClockOffsetResults=SS}",
            ["connectionHandle", "clockOffset"],
        ),
        "BluetoothL2CAPQualityOfServiceOptions": objc.createStructType(
            "IOBluetooth.BluetoothL2CAPQualityOfServiceOptions",
            b"{BluetoothL2CAPQualityOfServiceOptions=CCIIIII}",
            [
                "flags",
                "serviceType",
                "tokenRate",
                "tokenBucketSize",
                "peakBandwidth",
                "latency",
                "delayVariation",
            ],
        ),
        "IOBluetoothDeviceSearchDeviceAttributes": objc.createStructType(
            "IOBluetooth.IOBluetoothDeviceSearchDeviceAttributes",
            b"{IOBluetoothDeviceSearchDeviceAttributes={BluetoothDeviceAddress=[6C]}[248C]III}",
            [
                "address",
                "name",
                "serviceClassMajor",
                "deviceClassMajor",
                "deviceClassMinor",
            ],
        ),
        "BluetoothHCIFailedContactInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIFailedContactInfo",
            b"{BluetoothHCIFailedContactInfo=SS}",
            ["count", "handle"],
        ),
        "BluetoothHCIEnhancedAcceptSynchronousConnectionRequestParams": objc.createStructType(
            "IOBluetooth.BluetoothHCIEnhancedAcceptSynchronousConnectionRequestParams",
            b"{BluetoothHCIEnhancedAcceptSynchronousConnectionRequestParams=IIQQSSIIQQSSCCCCCCCCSSC}",
            [
                "transmitBandwidth",
                "receiveBandwidth",
                "transmitCodingFormat",
                "receiveCodingFormat",
                "transmitCodecFrameSize",
                "receiveCodecFrameSize",
                "inputBandwidth",
                "outputBandwidth",
                "inputCodingFormat",
                "outputCodingFormat",
                "inputCodedDataSize",
                "outputCodedDataSize",
                "inputPCMDataFormat",
                "outputPCMDataFormat",
                "inputPCMSamplePayloadMSBPosition",
                "outputPCMSamplePayloadMSBPosition",
                "inputDataPath",
                "outputDataPath",
                "inputTransportUnitSize",
                "outputTransportUnitSize",
                "maxLatency",
                "packetType",
                "retransmissionEffort",
            ],
        ),
        "BluetoothHCIEventPageScanRepetitionModeChangeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventPageScanRepetitionModeChangeResults",
            b"{BluetoothHCIEventPageScanRepetitionModeChangeResults={BluetoothDeviceAddress=[6C]}C}",
            ["deviceAddress", "pageScanRepetitionMode"],
        ),
        "BluetoothHCIVersionInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIVersionInfo",
            b"{BluetoothHCIVersionInfo=SCSCS}",
            [
                "manufacturerName",
                "lmpVersion",
                "lmpSubVersion",
                "hciVersion",
                "hciRevision",
            ],
        ),
        "BluetoothReadClockInfo": objc.createStructType(
            "IOBluetooth.BluetoothReadClockInfo",
            b"{BluetoothReadClockInfo=SIS}",
            ["handle", "clock", "accuracy"],
        ),
        "BluetoothHCICurrentInquiryAccessCodes": objc.createStructType(
            "IOBluetooth.BluetoothHCICurrentInquiryAccessCodes",
            b"{BluetoothHCICurrentInquiryAccessCodes=C^{BluetoothHCIInquiryAccessCode=[3C]}}",
            ["count", "codes"],
        ),
        "BluetoothSynchronousConnectionInfo": objc.createStructType(
            "IOBluetooth.BluetoothSynchronousConnectionInfo",
            b"{BluetoothSynchronousConnectionInfo=IISSCS}",
            [
                "transmitBandWidth",
                "receiveBandWidth",
                "maxLatency",
                "voiceSetting",
                "retransmissionEffort",
                "packetType",
            ],
        ),
        "BluetoothEventFilterCondition": objc.createStructType(
            "IOBluetooth.BluetoothEventFilterCondition",
            b"{BluetoothEventFilterCondition=[7C]}",
            ["data"],
        ),
        "BluetoothHCIEventFlowSpecificationData": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventFlowSpecificationData",
            b"{BluetoothHCIEventFlowSpecificationData=SCCCIIII}",
            [
                "connectionHandle",
                "flags",
                "flowDirection",
                "serviceType",
                "tokenRate",
                "tokenBucketSize",
                "peakBandwidth",
                "accessLatency",
            ],
        ),
        "BluetoothHCIEventDisconnectionCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventDisconnectionCompleteResults",
            b"{BluetoothHCIEventDisconnectionCompleteResults=SC}",
            ["connectionHandle", "reason"],
        ),
        "BluetoothHCIEventLEConnectionUpdateCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLEConnectionUpdateCompleteResults",
            b"{BluetoothHCIEventLEConnectionUpdateCompleteResults=SSSS}",
            ["connectionHandle", "connInterval", "connLatency", "supervisionTimeout"],
        ),
        "BluetoothHCIEventVendorSpecificResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventVendorSpecificResults",
            b"{BluetoothHCIEventVendorSpecificResults=C[255C]}",
            ["length", "data"],
        ),
        "BluetoothHCIEventSynchronousConnectionChangedResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventSynchronousConnectionChangedResults",
            b"{BluetoothHCIEventSynchronousConnectionChangedResults=SCCSS}",
            [
                "connectionHandle",
                "transmissionInterval",
                "retransmissionWindow",
                "receivePacketLength",
                "transmitPacketLength",
            ],
        ),
        "BluetoothHCISupportedFeatures": objc.createStructType(
            "IOBluetooth.BluetoothHCISupportedFeatures",
            b"{BluetoothHCISupportedFeatures=[8C]}",
            ["data"],
        ),
        "BluetoothHCISetupSynchronousConnectionParams": objc.createStructType(
            "IOBluetooth.BluetoothHCISetupSynchronousConnectionParams",
            b"{BluetoothHCISetupSynchronousConnectionParams=IISSCS}",
            [
                "transmitBandwidth",
                "receiveBandwidth",
                "maxLatency",
                "voiceSetting",
                "retransmissionEffort",
                "packetType",
            ],
        ),
        "OBEXConnectCommandData": objc.createStructType(
            "IOBluetooth.OBEXConnectCommandData",
            b"{OBEXConnectCommandData=^vQSCC}",
            ["headerDataPtr", "headerDataLength", "maxPacketSize", "version", "flags"],
        ),
        "BluetoothHCIEventSimplePairingCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventSimplePairingCompleteResults",
            b"{BluetoothHCIEventSimplePairingCompleteResults={BluetoothDeviceAddress=[6C]}}",
            ["deviceAddress"],
        ),
        "BluetoothHCICurrentInquiryAccessCodesForWrite": objc.createStructType(
            "IOBluetooth.BluetoothHCICurrentInquiryAccessCodesForWrite",
            b"{BluetoothHCICurrentInquiryAccessCodesForWrite=C[192C]}",
            ["count", "codes"],
        ),
        "BluetoothHCIEventReadRemoteExtendedFeaturesResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventReadRemoteExtendedFeaturesResults",
            b"{BluetoothHCIEventReadRemoteExtendedFeaturesResults=CSCC{BluetoothHCISupportedFeatures=[8C]}}",
            ["error", "connectionHandle", "page", "maxPage", "lmpFeatures"],
        ),
        "BluetoothHCITransmitPowerLevelInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCITransmitPowerLevelInfo",
            b"{BluetoothHCITransmitPowerLevelInfo=Sc}",
            ["handle", "level"],
        ),
        "BluetoothHCIExtendedInquiryResult": objc.createStructType(
            "IOBluetooth.BluetoothHCIExtendedInquiryResult",
            b"{BluetoothHCIExtendedInquiryResult=C{BluetoothDeviceAddress=[6C]}CCISc{BluetoothHCIExtendedInquiryResponse=[240C]}}",
            [
                "numberOfReponses",
                "deviceAddress",
                "pageScanRepetitionMode",
                "reserved",
                "classOfDevice",
                "clockOffset",
                "RSSIValue",
                "extendedInquiryResponse",
            ],
        ),
        "BluetoothHCIEventSniffSubratingResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventSniffSubratingResults",
            b"{BluetoothHCIEventSniffSubratingResults=SSSSS}",
            [
                "connectionHandle",
                "maxTransmitLatency",
                "maxReceiveLatency",
                "minRemoteTimeout",
                "minLocalTimeout",
            ],
        ),
        "BluetoothHCIEventReadExtendedFeaturesResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventReadExtendedFeaturesResults",
            b"{BluetoothHCIEventReadExtendedFeaturesResults=S{BluetoothHCIExtendedFeaturesInfo=CC[8C]}}",
            ["connectionHandle", "supportedFeaturesInfo"],
        ),
        "BluetoothHCIEventQoSSetupCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventQoSSetupCompleteResults",
            b"{BluetoothHCIEventQoSSetupCompleteResults=S{BluetoothHCIQualityOfServiceSetupParams=CCIIII}}",
            ["connectionHandle", "setupParams"],
        ),
        "BluetoothHCIEnhancedSetupSynchronousConnectionParams": objc.createStructType(
            "IOBluetooth.BluetoothHCIEnhancedSetupSynchronousConnectionParams",
            b"{BluetoothHCIEnhancedSetupSynchronousConnectionParams=IIQQSSIIQQSSCCCCCCCCSSC}",
            [
                "transmitBandwidth",
                "receiveBandwidth",
                "transmitCodingFormat",
                "receiveCodingFormat",
                "transmitCodecFrameSize",
                "receiveCodecFrameSize",
                "inputBandwidth",
                "outputBandwidth",
                "inputCodingFormat",
                "outputCodingFormat",
                "inputCodedDataSize",
                "outputCodedDataSize",
                "inputPCMDataFormat",
                "outputPCMDataFormat",
                "inputPCMSamplePayloadMSBPosition",
                "outputPCMSamplePayloadMSBPosition",
                "inputDataPath",
                "outputDataPath",
                "inputTransportUnitSize",
                "outputTransportUnitSize",
                "maxLatency",
                "packetType",
                "retransmissionEffort",
            ],
        ),
        "OBEXGetCommandData": objc.createStructType(
            "IOBluetooth.OBEXGetCommandData",
            b"{OBEXGetCommandData=^vQ}",
            ["headerDataPtr", "headerDataLength"],
        ),
        "BluetoothHCIEventEncryptionKeyRefreshCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventEncryptionKeyRefreshCompleteResults",
            b"{BluetoothHCIEventEncryptionKeyRefreshCompleteResults=S}",
            ["connectionHandle"],
        ),
        "BluetoothHCILinkPolicySettingsInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCILinkPolicySettingsInfo",
            b"{BluetoothHCILinkPolicySettingsInfo=SS}",
            ["settings", "handle"],
        ),
        "BluetoothHCIEventReadRemoteVersionInfoResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventReadRemoteVersionInfoResults",
            b"{BluetoothHCIEventReadRemoteVersionInfoResults=SCSS}",
            ["connectionHandle", "lmpVersion", "manufacturerName", "lmpSubversion"],
        ),
        "BluetoothHCIStoredLinkKeysInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIStoredLinkKeysInfo",
            b"{BluetoothHCIStoredLinkKeysInfo=SS}",
            ["numLinkKeysRead", "maxNumLinkKeysAllowedInDevice"],
        ),
        "BluetoothHCIEncryptionKeySizeInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIEncryptionKeySizeInfo",
            b"{BluetoothHCIEncryptionKeySizeInfo=SC}",
            ["handle", "keySize"],
        ),
        "BluetoothHCIInquiryResult": objc.createStructType(
            "IOBluetooth.BluetoothHCIInquiryResult",
            b"{BluetoothHCIInquiryResult={BluetoothDeviceAddress=[6C]}CCCIS}",
            [
                "deviceAddress",
                "pageScanRepetitionMode",
                "pageScanPeriodMode",
                "pageScanMode",
                "classOfDevice",
                "clockOffset",
            ],
        ),
        "BluetoothHCIEventConnectionPacketTypeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventConnectionPacketTypeResults",
            b"{BluetoothHCIEventConnectionPacketTypeResults=SS}",
            ["connectionHandle", "packetType"],
        ),
        "BluetoothHCIEventEncryptionChangeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventEncryptionChangeResults",
            b"{BluetoothHCIEventEncryptionChangeResults=SC}",
            ["connectionHandle", "enable"],
        ),
        "BluetoothHCILinkSupervisionTimeout": objc.createStructType(
            "IOBluetooth.BluetoothHCILinkSupervisionTimeout",
            b"{BluetoothHCILinkSupervisionTimeout=SS}",
            ["handle", "timeout"],
        ),
        "BluetoothHCIInquiryAccessCode": objc.createStructType(
            "IOBluetooth.BluetoothHCIInquiryAccessCode",
            b"{BluetoothHCIInquiryAccessCode=[3C]}",
            ["data"],
        ),
        "BluetoothHCIQualityOfServiceSetupParams": objc.createStructType(
            "IOBluetooth.BluetoothHCIQualityOfServiceSetupParams",
            b"{BluetoothHCIQualityOfServiceSetupParams=CCIIII}",
            [
                "flags",
                "serviceType",
                "tokenRate",
                "peakBandwidth",
                "latency",
                "delayVariation",
            ],
        ),
        "BluetoothHCISimplePairingOOBData": objc.createStructType(
            "IOBluetooth.BluetoothHCISimplePairingOOBData",
            b"{BluetoothHCISimplePairingOOBData=[16C]}",
            ["data"],
        ),
        "OBEXSetPathCommandResponseData": objc.createStructType(
            "IOBluetooth.OBEXSetPathCommandResponseData",
            b"{OBEXSetPathCommandResponseData=C^vQCC}",
            [
                "serverResponseOpCode",
                "headerDataPtr",
                "headerDataLength",
                "flags",
                "constants",
            ],
        ),
        "IOBluetoothDeviceSearchAttributes": objc.createStructType(
            "IOBluetooth.IOBluetoothDeviceSearchAttributes",
            b"{IOBluetoothDeviceSearchAttributes=III^{IOBluetoothDeviceSearchDeviceAttributes={BluetoothDeviceAddress=[6C]}[248C]III}}",
            ["options", "maxResults", "deviceAttributeCount", "attributeList"],
        ),
        "BluetoothEnhancedSynchronousConnectionInfo": objc.createStructType(
            "IOBluetooth.BluetoothEnhancedSynchronousConnectionInfo",
            b"{BluetoothEnhancedSynchronousConnectionInfo=IIQQSSIIQQSSCCCCCCCCSSCS}",
            [
                "transmitBandWidth",
                "receiveBandWidth",
                "transmitCodingFormat",
                "receiveCodingFormat",
                "transmitCodecFrameSize",
                "receiveCodecFrameSize",
                "inputBandwidth",
                "outputBandwidth",
                "inputCodingFormat",
                "outputCodingFormat",
                "inputCodedDataSize",
                "outputCodedDataSize",
                "inputPCMDataFormat",
                "outputPCMDataFormat",
                "inputPCMSampelPayloadMSBPosition",
                "outputPCMSampelPayloadMSBPosition",
                "inputDataPath",
                "outputDataPath",
                "inputTransportUnitSize",
                "outputTransportUnitSize",
                "maxLatency",
                "voiceSetting",
                "retransmissionEffort",
                "packetType",
            ],
        ),
        "BluetoothHCIEventPageScanModeChangeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventPageScanModeChangeResults",
            b"{BluetoothHCIEventPageScanModeChangeResults={BluetoothDeviceAddress=[6C]}C}",
            ["deviceAddress", "pageScanMode"],
        ),
        "BluetoothHCIEventFlushOccurredResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventFlushOccurredResults",
            b"{BluetoothHCIEventFlushOccurredResults=S}",
            ["connectionHandle"],
        ),
        "BluetoothHCIExtendedInquiryResponse": objc.createStructType(
            "IOBluetooth.BluetoothHCIExtendedInquiryResponse",
            b"{BluetoothHCIExtendedInquiryResponse=[240C]}",
            ["data"],
        ),
        "BluetoothHCIEventLELongTermKeyRequestResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLELongTermKeyRequestResults",
            b"{BluetoothHCIEventLELongTermKeyRequestResults=S[8C]S}",
            ["connectionHandle", "randomNumber", "ediv"],
        ),
        "BluetoothHCIEventConnectionRequestResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventConnectionRequestResults",
            b"{BluetoothHCIEventConnectionRequestResults={BluetoothDeviceAddress=[6C]}IC}",
            ["deviceAddress", "classOfDevice", "linkType"],
        ),
        "BluetoothAFHResults": objc.createStructType(
            "IOBluetooth.BluetoothAFHResults",
            b"{BluetoothAFHResults=SC[10C]}",
            ["handle", "mode", "afhMap"],
        ),
        "BluetoothSetEventMask": objc.createStructType(
            "IOBluetooth.BluetoothSetEventMask",
            b"{BluetoothSetEventMask=[8C]}",
            ["data"],
        ),
        "BluetoothUserPasskeyNotification": objc.createStructType(
            "IOBluetooth.BluetoothUserPasskeyNotification",
            b"{BluetoothUserPasskeyNotification={BluetoothDeviceAddress=[6C]}I}",
            ["deviceAddress", "passkey"],
        ),
        "BluetoothHCIEventQoSViolationResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventQoSViolationResults",
            b"{BluetoothHCIEventQoSViolationResults=S}",
            ["connectionHandle"],
        ),
        "OBEXDisconnectCommandResponseData": objc.createStructType(
            "IOBluetooth.OBEXDisconnectCommandResponseData",
            b"{OBEXDisconnectCommandResponseData=C^vQ}",
            ["serverResponseOpCode", "headerDataPtr", "headerDataLength"],
        ),
        "OBEXAbortCommandResponseData": objc.createStructType(
            "IOBluetooth.OBEXAbortCommandResponseData",
            b"{OBEXAbortCommandResponseData=C^vQ}",
            ["serverResponseOpCode", "headerDataPtr", "headerDataLength"],
        ),
        "BluetoothHCIEventAuthenticationCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventAuthenticationCompleteResults",
            b"{BluetoothHCIEventAuthenticationCompleteResults=S}",
            ["connectionHandle"],
        ),
        "BluetoothHCIEventRoleChangeResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventRoleChangeResults",
            b"{BluetoothHCIEventRoleChangeResults=S{BluetoothDeviceAddress=[6C]}C}",
            ["connectionHandle", "deviceAddress", "role"],
        ),
        "BluetoothHCIEventLEReadRemoteUsedFeaturesCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLEReadRemoteUsedFeaturesCompleteResults",
            b"{BluetoothHCIEventLEReadRemoteUsedFeaturesCompleteResults=S{BluetoothHCISupportedFeatures=[8C]}}",
            ["connectionHandle", "usedFeatures"],
        ),
        "BluetoothHCIEventLEEnhancedConnectionCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLEEnhancedConnectionCompleteResults",
            b"{BluetoothHCIEventLEEnhancedConnectionCompleteResults=SCC{BluetoothDeviceAddress=[6C]}{BluetoothDeviceAddress=[6C]}{BluetoothDeviceAddress=[6C]}SSSC}",
            [
                "connectionHandle",
                "role",
                "peerAddressType",
                "peerAddress",
                "localResolvablePrivateAddress",
                "peerResolvablePrivateAddress",
                "connInterval",
                "connLatency",
                "supervisionTimeout",
                "masterClockAccuracy",
            ],
        ),
        "BluetoothHCIRSSIInfo": objc.createStructType(
            "IOBluetooth.BluetoothHCIRSSIInfo",
            b"{BluetoothHCIRSSIInfo=Sc}",
            ["handle", "RSSIValue"],
        ),
        "BluetoothHCIEventLEConnectionCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventLEConnectionCompleteResults",
            b"{BluetoothHCIEventLEConnectionCompleteResults=SCC{BluetoothDeviceAddress=[6C]}SSSC}",
            [
                "connectionHandle",
                "role",
                "peerAddressType",
                "peerAddress",
                "connInterval",
                "connLatency",
                "supervisionTimeout",
                "masterClockAccuracy",
            ],
        ),
        "BluetoothKeypressNotification": objc.createStructType(
            "IOBluetooth.BluetoothKeypressNotification",
            b"{BluetoothKeypressNotification={BluetoothDeviceAddress=[6C]}C}",
            ["deviceAddress", "notificationType"],
        ),
        "BluetoothHCIReadExtendedInquiryResponseResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIReadExtendedInquiryResponseResults",
            b"{BluetoothHCIReadExtendedInquiryResponseResults=C{BluetoothHCIExtendedInquiryResponse=[240C]}}",
            ["outFECRequired", "extendedInquiryResponse"],
        ),
        "BluetoothIOCapabilityResponse": objc.createStructType(
            "IOBluetooth.BluetoothIOCapabilityResponse",
            b"{BluetoothIOCapabilityResponse={BluetoothDeviceAddress=[6C]}CCC}",
            [
                "deviceAddress",
                "ioCapability",
                "OOBDataPresence",
                "authenticationRequirements",
            ],
        ),
        "BluetoothHCIBufferSize": objc.createStructType(
            "IOBluetooth.BluetoothHCIBufferSize",
            b"{BluetoothHCIBufferSize=SCSS}",
            [
                "ACLDataPacketLength",
                "SCODataPacketLength",
                "totalNumACLDataPackets",
                "totalNumSCODataPackets",
            ],
        ),
        "OBEXTransportEvent": objc.createStructType(
            "IOBluetooth.OBEXTransportEvent",
            b"{OBEXTransportEvent=Ii^vQ}",
            ["type", "status", "dataPtr", "dataLength"],
        ),
        "BluetoothHCIInquiryWithRSSIResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIInquiryWithRSSIResults",
            b"{BluetoothHCIInquiryWithRSSIResults=[50{BluetoothHCIInquiryWithRSSIResult={BluetoothDeviceAddress=[6C]}CCISc}]I}",
            ["results", "count"],
        ),
        "BluetoothAFHHostChannelClassification": objc.createStructType(
            "IOBluetooth.BluetoothAFHHostChannelClassification",
            b"{BluetoothAFHHostChannelClassification=[10C]}",
            ["data"],
        ),
        "BluetoothHCIEventRemoteNameRequestResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventRemoteNameRequestResults",
            b"{BluetoothHCIEventRemoteNameRequestResults={BluetoothDeviceAddress=[6C]}[248C]}",
            ["deviceAddress", "deviceName"],
        ),
        "BluetoothHCIEventChangeConnectionLinkKeyCompleteResults": objc.createStructType(
            "IOBluetooth.BluetoothHCIEventChangeConnectionLinkKeyCompleteResults",
            b"{BluetoothHCIEventChangeConnectionLinkKeyCompleteResults=S}",
            ["connectionHandle"],
        ),
        "BluetoothUserConfirmationRequest": objc.createStructType(
            "IOBluetooth.BluetoothUserConfirmationRequest",
            b"{BluetoothUserConfirmationRequest={BluetoothDeviceAddress=[6C]}I}",
            ["deviceAddress", "numericValue"],
        ),
        "BluetoothTransportInfo": objc.createStructType(
            "IOBluetooth.BluetoothTransportInfo",
            b"{BluetoothTransportInfo=III[35c][35c]QQQQ}",
            [
                "productID",
                "vendorID",
                "type",
                "productName",
                "vendorName",
                "totalDataBytesSent",
                "totalSCOBytesSent",
                "totalDataBytesReceived",
                "totalSCOBytesReceived",
            ],
        ),
    }
)
constants = """$IOBluetoothHandsFreeCallDirection$IOBluetoothHandsFreeCallIndex$IOBluetoothHandsFreeCallMode$IOBluetoothHandsFreeCallMultiparty$IOBluetoothHandsFreeCallName$IOBluetoothHandsFreeCallNumber$IOBluetoothHandsFreeCallStatus$IOBluetoothHandsFreeCallType$IOBluetoothHandsFreeIndicatorBattChg$IOBluetoothHandsFreeIndicatorCall$IOBluetoothHandsFreeIndicatorCallHeld$IOBluetoothHandsFreeIndicatorCallSetup$IOBluetoothHandsFreeIndicatorRoam$IOBluetoothHandsFreeIndicatorService$IOBluetoothHandsFreeIndicatorSignal$IOBluetoothHostControllerPoweredOffNotification$IOBluetoothHostControllerPoweredOnNotification$IOBluetoothL2CAPChannelPublishedNotification$IOBluetoothL2CAPChannelTerminatedNotification$IOBluetoothPDUEncoding$IOBluetoothPDUOriginatingAddress$IOBluetoothPDUOriginatingAddressType$IOBluetoothPDUProtocolID$IOBluetoothPDUServicCenterAddress$IOBluetoothPDUServiceCenterAddressType$IOBluetoothPDUTimestamp$IOBluetoothPDUType$IOBluetoothPDUUserData$kFTSListingNameKey$kFTSListingSizeKey$kFTSListingTypeKey$kFTSProgressBytesTotalKey$kFTSProgressBytesTransferredKey$kFTSProgressEstimatedTimeKey$kFTSProgressPercentageKey$kFTSProgressPrecentageKey$kFTSProgressTimeElapsedKey$kFTSProgressTransferRateKey$kOBEXHeaderIDKeyAppParameters$kOBEXHeaderIDKeyAuthorizationChallenge$kOBEXHeaderIDKeyAuthorizationResponse$kOBEXHeaderIDKeyBody$kOBEXHeaderIDKeyByteSequence$kOBEXHeaderIDKeyConnectionID$kOBEXHeaderIDKeyCount$kOBEXHeaderIDKeyDescription$kOBEXHeaderIDKeyEndOfBody$kOBEXHeaderIDKeyHTTP$kOBEXHeaderIDKeyLength$kOBEXHeaderIDKeyName$kOBEXHeaderIDKeyObjectClass$kOBEXHeaderIDKeyTarget$kOBEXHeaderIDKeyTime4Byte$kOBEXHeaderIDKeyTimeISO$kOBEXHeaderIDKeyType$kOBEXHeaderIDKeyUnknown1ByteQuantity$kOBEXHeaderIDKeyUnknown4ByteQuantity$kOBEXHeaderIDKeyUnknownByteSequence$kOBEXHeaderIDKeyUnknownUnicodeText$kOBEXHeaderIDKeyUserDefined$kOBEXHeaderIDKeyWho$"""
enums = """$BluetoothLEAddressTypePublic@0$BluetoothLEAddressTypeRandom@1$BluetoothLEAdvertisingTypeConnectableDirected@1$BluetoothLEAdvertisingTypeConnectableUndirected@0$BluetoothLEAdvertisingTypeDiscoverableUndirected@2$BluetoothLEAdvertisingTypeNonConnectableUndirected@3$BluetoothLEAdvertisingTypeScanResponse@4$BluetoothLEConnectionIntervalMax@3200$BluetoothLEConnectionIntervalMin@6$BluetoothLEScanDisable@0$BluetoothLEScanDuplicateFilterDisable@0$BluetoothLEScanDuplicateFilterEnable@1$BluetoothLEScanEnable@1$BluetoothLEScanFilterNone@0$BluetoothLEScanFilterSafelist@1$BluetoothLEScanFilterWhitelist@1$BluetoothLEScanTypeActive@1$BluetoothLEScanTypePassive@0$BluetoothRFCOMMLineStatusFramingError@3$BluetoothRFCOMMLineStatusNoError@0$BluetoothRFCOMMLineStatusOverrunError@1$BluetoothRFCOMMLineStatusParityError@2$IOBluetoothHandsFreeAudioGatewayFeatureAttachedNumberToVoiceTag@16$IOBluetoothHandsFreeAudioGatewayFeatureCodecNegotiation@512$IOBluetoothHandsFreeAudioGatewayFeatureECAndOrNRFunction@2$IOBluetoothHandsFreeAudioGatewayFeatureEnhancedCallControl@128$IOBluetoothHandsFreeAudioGatewayFeatureEnhancedCallStatus@64$IOBluetoothHandsFreeAudioGatewayFeatureExtendedErrorResultCodes@256$IOBluetoothHandsFreeAudioGatewayFeatureInBandRingTone@8$IOBluetoothHandsFreeAudioGatewayFeatureNone@0$IOBluetoothHandsFreeAudioGatewayFeatureRejectCallCapability@32$IOBluetoothHandsFreeAudioGatewayFeatureThreeWayCalling@1$IOBluetoothHandsFreeAudioGatewayFeatureVoiceRecognition@4$IOBluetoothHandsFreeCallHoldMode0@1$IOBluetoothHandsFreeCallHoldMode1@2$IOBluetoothHandsFreeCallHoldMode1idx@4$IOBluetoothHandsFreeCallHoldMode2@8$IOBluetoothHandsFreeCallHoldMode2idx@16$IOBluetoothHandsFreeCallHoldMode3@32$IOBluetoothHandsFreeCallHoldMode4@64$IOBluetoothHandsFreeCodecIDAACELD@128$IOBluetoothHandsFreeCodecIDCVSD@1$IOBluetoothHandsFreeCodecIDmSBC@2$IOBluetoothHandsFreeDeviceFeatureCLIPresentation@4$IOBluetoothHandsFreeDeviceFeatureCodecNegotiation@128$IOBluetoothHandsFreeDeviceFeatureECAndOrNRFunction@1$IOBluetoothHandsFreeDeviceFeatureEnhancedCallControl@64$IOBluetoothHandsFreeDeviceFeatureEnhancedCallStatus@32$IOBluetoothHandsFreeDeviceFeatureNone@0$IOBluetoothHandsFreeDeviceFeatureRemoteVolumeControl@16$IOBluetoothHandsFreeDeviceFeatureThreeWayCalling@2$IOBluetoothHandsFreeDeviceFeatureVoiceRecognition@8$IOBluetoothHandsFreeManufactureSpecificSMSSupport@4$IOBluetoothHandsFreePDUStatusAll@4$IOBluetoothHandsFreePDUStatusRecRead@1$IOBluetoothHandsFreePDUStatusRecUnread@0$IOBluetoothHandsFreePDUStatusStoSent@3$IOBluetoothHandsFreePDUStatusStoUnsent@2$IOBluetoothHandsFreePhase2SMSSupport@1$IOBluetoothHandsFreePhase2pSMSSupport@2$IOBluetoothSMSModePDU@0$IOBluetoothSMSModeText@1$KBluetoothExtendedFeatureSecureConnectionsHostMode@8$kAFHChannelAssessmentModeDisabled@0$kAFHChannelAssessmentModeEnabled@1$kAuthenticationDisabled@0$kAuthenticationEnabled@1$kBluetoothACLConnection@1$kBluetoothACLLogicalChannelL2CAPContinue@1$kBluetoothACLLogicalChannelL2CAPStart@2$kBluetoothACLLogicalChannelLMP@3$kBluetoothACLLogicalChannelReserved@0$kBluetoothAMPManagerCodeAMPChangeNotify@4$kBluetoothAMPManagerCodeAMPChangeResponse@5$kBluetoothAMPManagerCodeAMPCommandReject@1$kBluetoothAMPManagerCodeAMPCreatePhysicalLinkRequest@10$kBluetoothAMPManagerCodeAMPCreatePhysicalLinkResponse@11$kBluetoothAMPManagerCodeAMPDisconnectPhysicalLinkRequest@12$kBluetoothAMPManagerCodeAMPDisconnectPhysicalLinkResponse@13$kBluetoothAMPManagerCodeAMPDiscoverRequest@2$kBluetoothAMPManagerCodeAMPDiscoverResponse@3$kBluetoothAMPManagerCodeAMPGetAssocRequest@8$kBluetoothAMPManagerCodeAMPGetAssocResponse@9$kBluetoothAMPManagerCodeAMPGetInfoRequest@6$kBluetoothAMPManagerCodeAMPGetInfoResponse@7$kBluetoothAMPManagerCodeReserved@0$kBluetoothAMPManagerCommandRejectReasonCommandNotRecognized@0$kBluetoothAMPManagerCreatePhysicalLinkResponseAMPDisconnectedPhysicalLinkRequestReceived@4$kBluetoothAMPManagerCreatePhysicalLinkResponseCollisionOccurred@3$kBluetoothAMPManagerCreatePhysicalLinkResponseInvalidControllerID@1$kBluetoothAMPManagerCreatePhysicalLinkResponsePhysicalLinkAlreadyExists@5$kBluetoothAMPManagerCreatePhysicalLinkResponseSecurityViolation@6$kBluetoothAMPManagerCreatePhysicalLinkResponseSuccess@0$kBluetoothAMPManagerCreatePhysicalLinkResponseUnableToStartLinkCreation@2$kBluetoothAMPManagerDisconnectPhysicalLinkResponseInvalidControllerID@1$kBluetoothAMPManagerDisconnectPhysicalLinkResponseNoPhysicalLink@2$kBluetoothAMPManagerDisconnectPhysicalLinkResponseSuccess@0$kBluetoothAMPManagerDiscoverResponseControllerStatusBluetoothOnly@1$kBluetoothAMPManagerDiscoverResponseControllerStatusFullCapacity@6$kBluetoothAMPManagerDiscoverResponseControllerStatusHighCapacity@5$kBluetoothAMPManagerDiscoverResponseControllerStatusLowCapacity@3$kBluetoothAMPManagerDiscoverResponseControllerStatusMediumCapacity@4$kBluetoothAMPManagerDiscoverResponseControllerStatusNoCapacity@2$kBluetoothAMPManagerDiscoverResponseControllerStatusPoweredDown@0$kBluetoothAMPManagerGetAssocResponseInvalidControllerID@1$kBluetoothAMPManagerGetAssocResponseSuccess@0$kBluetoothAMPManagerGetInfoResponseInvalidControllerID@1$kBluetoothAMPManagerGetInfoResponseSuccess@0$kBluetoothAirModeALawLog@1$kBluetoothAirModeCVSD@2$kBluetoothAirModeTransparentData@3$kBluetoothAirModeULawLog@0$kBluetoothAllowRoleSwitch@1$kBluetoothAuthenticationRequirementsMITMProtectionNotRequired@0$kBluetoothAuthenticationRequirementsMITMProtectionNotRequiredDedicatedBonding@2$kBluetoothAuthenticationRequirementsMITMProtectionNotRequiredGeneralBonding@4$kBluetoothAuthenticationRequirementsMITMProtectionNotRequiredNoBonding@0$kBluetoothAuthenticationRequirementsMITMProtectionRequired@1$kBluetoothAuthenticationRequirementsMITMProtectionRequiredDedicatedBonding@3$kBluetoothAuthenticationRequirementsMITMProtectionRequiredGeneralBonding@5$kBluetoothAuthenticationRequirementsMITMProtectionRequiredNoBonding@1$kBluetoothCapabilityTypeDisplayOnly@0$kBluetoothCapabilityTypeDisplayYesNo@1$kBluetoothCapabilityTypeKeyboardOnly@2$kBluetoothCapabilityTypeNoInputNoOutput@3$kBluetoothCompanyIdentifer3Com@5$kBluetoothCompanyIdentifer3DSP@73$kBluetoothCompanyIdentifer3DiJoy@84$kBluetoothCompanyIdentifer9SolutionsOy@102$kBluetoothCompanyIdentiferAAMPofAmerica@190$kBluetoothCompanyIdentiferAAndDEngineering@105$kBluetoothCompanyIdentiferAAndRCambridge@124$kBluetoothCompanyIdentiferACTSTechnologies@232$kBluetoothCompanyIdentiferAMICCOMElectronics@192$kBluetoothCompanyIdentiferAPT@79$kBluetoothCompanyIdentiferARCHOS@207$kBluetoothCompanyIdentiferARPDevicesUnlimited@168$kBluetoothCompanyIdentiferAVMBerlin@31$kBluetoothCompanyIdentiferAboveAverageOutcomes@238$kBluetoothCompanyIdentiferAccelSemiconductor@74$kBluetoothCompanyIdentiferAceSensor@188$kBluetoothCompanyIdentiferAceUni@248$kBluetoothCompanyIdentiferAdidas@195$kBluetoothCompanyIdentiferAdvancedPANMOBILSystems@145$kBluetoothCompanyIdentiferAirohaTechnology@148$kBluetoothCompanyIdentiferAlcatel@36$kBluetoothCompanyIdentiferAlpwise@154$kBluetoothCompanyIdentiferAplix@189$kBluetoothCompanyIdentiferApple@76$kBluetoothCompanyIdentiferAtherosCommunications@69$kBluetoothCompanyIdentiferAtmel@19$kBluetoothCompanyIdentiferAustcoCommunicationsSystems@213$kBluetoothCompanyIdentiferAutonetMobile@127$kBluetoothCompanyIdentiferAvagoTechnologies@78$kBluetoothCompanyIdentiferBDETechnology@180$kBluetoothCompanyIdentiferBandXIInternational@100$kBluetoothCompanyIdentiferBandspeed@32$kBluetoothCompanyIdentiferBangAndOlufson@259$kBluetoothCompanyIdentiferBeatsElectronics@204$kBluetoothCompanyIdentiferBeautifulEnterprise@108$kBluetoothCompanyIdentiferBekey@178$kBluetoothCompanyIdentiferBelkinInternational@92$kBluetoothCompanyIdentiferBinauricSE@203$kBluetoothCompanyIdentiferBioResearchAssociates@236$kBluetoothCompanyIdentiferBiosentronics@219$kBluetoothCompanyIdentiferBitsplitters@239$kBluetoothCompanyIdentiferBlueRadios@133$kBluetoothCompanyIdentiferBluegiga@71$kBluetoothCompanyIdentiferBluetoothSIG@63$kBluetoothCompanyIdentiferBose@158$kBluetoothCompanyIdentiferBriarTek@109$kBluetoothCompanyIdentiferBroadcom@15$kBluetoothCompanyIdentiferCATC@52$kBluetoothCompanyIdentiferCONWISETechnology@66$kBluetoothCompanyIdentiferCTechnologies@38$kBluetoothCompanyIdentiferCaenRFID@170$kBluetoothCompanyIdentiferCambridgeSiliconRadio@10$kBluetoothCompanyIdentiferCinetix@175$kBluetoothCompanyIdentiferClarinoxTechnologies@179$kBluetoothCompanyIdentiferColorfy@156$kBluetoothCompanyIdentiferCommil@51$kBluetoothCompanyIdentiferConexantSystems@28$kBluetoothCompanyIdentiferConnectBlueAB@113$kBluetoothCompanyIdentiferConnecteDevice@151$kBluetoothCompanyIdentiferContinentialAutomotiveSystems@75$kBluetoothCompanyIdentiferCreativeTechnology@118$kBluetoothCompanyIdentiferCrystalCode@250$kBluetoothCompanyIdentiferDanlers@225$kBluetoothCompanyIdentiferDeLormePublishingCompany@128$kBluetoothCompanyIdentiferDelphi@252$kBluetoothCompanyIdentiferDexcom@208$kBluetoothCompanyIdentiferDialogSemiconductor@210$kBluetoothCompanyIdentiferDigianswerAS@12$kBluetoothCompanyIdentiferEMMicroElectronicMarin@90$kBluetoothCompanyIdentiferEclipse@53$kBluetoothCompanyIdentiferEcotest@136$kBluetoothCompanyIdentiferEdenSoftwareConsultants@229$kBluetoothCompanyIdentiferElcometer@246$kBluetoothCompanyIdentiferElgatoSystems@206$kBluetoothCompanyIdentiferEquinux@134$kBluetoothCompanyIdentiferEricssonTechnologyLicensing@0$kBluetoothCompanyIdentiferEvluma@201$kBluetoothCompanyIdentiferFree2Move@83$kBluetoothCompanyIdentiferFreshtemp@230$kBluetoothCompanyIdentiferFuGoo@257$kBluetoothCompanyIdentiferFunaiElectric@144$kBluetoothCompanyIdentiferGCTSemiconductor@45$kBluetoothCompanyIdentiferGNNetcom@103$kBluetoothCompanyIdentiferGNResound@137$kBluetoothCompanyIdentiferGarminInternational@135$kBluetoothCompanyIdentiferGeLo@200$kBluetoothCompanyIdentiferGeneq@194$kBluetoothCompanyIdentiferGeneralMotors@104$kBluetoothCompanyIdentiferGennum@59$kBluetoothCompanyIdentiferGeoforce@157$kBluetoothCompanyIdentiferGibsonGuitars@98$kBluetoothCompanyIdentiferGimbal@140$kBluetoothCompanyIdentiferGoogle@224$kBluetoothCompanyIdentiferGreenThrottleGames@172$kBluetoothCompanyIdentiferGroupSense@115$kBluetoothCompanyIdentiferHanlynnTechnologies@123$kBluetoothCompanyIdentiferHarmonInternational@87$kBluetoothCompanyIdentiferHewlettPackard@101$kBluetoothCompanyIdentiferHitachi@41$kBluetoothCompanyIdentiferHosiden@221$kBluetoothCompanyIdentiferIBM@3$kBluetoothCompanyIdentiferIPextreme@61$kBluetoothCompanyIdentiferITechDynamicGlobalDistribution@153$kBluetoothCompanyIdentiferInMusicBrands@227$kBluetoothCompanyIdentiferInfineonTechnologiesAG@9$kBluetoothCompanyIdentiferIngenieurSystemgruppeZahn@171$kBluetoothCompanyIdentiferInnovativeYachtterSolutions@262$kBluetoothCompanyIdentiferIntegratedSiliconSolution@65$kBluetoothCompanyIdentiferIntegratedSystemSolution@57$kBluetoothCompanyIdentiferIntel@2$kBluetoothCompanyIdentiferInteropIdentifier@65535$kBluetoothCompanyIdentiferInventel@30$kBluetoothCompanyIdentiferJandM@82$kBluetoothCompanyIdentiferJawbone@138$kBluetoothCompanyIdentiferJiangsuToppowerAutomotiveElectronics@155$kBluetoothCompanyIdentiferJohnsonControls@185$kBluetoothCompanyIdentiferJollyLogic@237$kBluetoothCompanyIdentiferKCTechnology@22$kBluetoothCompanyIdentiferKOUKAMM@251$kBluetoothCompanyIdentiferKSTechnologies@231$kBluetoothCompanyIdentiferKawantech@212$kBluetoothCompanyIdentiferKeiser@258$kBluetoothCompanyIdentiferKensingtonComputerProductsGroup@160$kBluetoothCompanyIdentiferKentDisplays@243$kBluetoothCompanyIdentiferLGElectronics@196$kBluetoothCompanyIdentiferLSResearch@228$kBluetoothCompanyIdentiferLairdTechnologies@119$kBluetoothCompanyIdentiferLessWire@121$kBluetoothCompanyIdentiferLinak@164$kBluetoothCompanyIdentiferLucent@7$kBluetoothCompanyIdentiferLudusHelsinki@132$kBluetoothCompanyIdentiferMC10@202$kBluetoothCompanyIdentiferMStarTechnologies@122$kBluetoothCompanyIdentiferMacronixInternational@44$kBluetoothCompanyIdentiferMagnetiMarelli@169$kBluetoothCompanyIdentiferMansella@33$kBluetoothCompanyIdentiferMarvellTechnologyGroup@72$kBluetoothCompanyIdentiferMatsushitaElectricIndustrial@58$kBluetoothCompanyIdentiferMediaTek@70$kBluetoothCompanyIdentiferMesoInternational@182$kBluetoothCompanyIdentiferMetaWatch@163$kBluetoothCompanyIdentiferMewTelTechnology@47$kBluetoothCompanyIdentiferMiCommand@99$kBluetoothCompanyIdentiferMicrochipTechnology@205$kBluetoothCompanyIdentiferMicrosoft@6$kBluetoothCompanyIdentiferMindTree@106$kBluetoothCompanyIdentiferMisfitWearables@223$kBluetoothCompanyIdentiferMistubishiElectric@20$kBluetoothCompanyIdentiferMitelSemiconductor@16$kBluetoothCompanyIdentiferMobilian@55$kBluetoothCompanyIdentiferMonster@112$kBluetoothCompanyIdentiferMorseProject@242$kBluetoothCompanyIdentiferMotorola@8$kBluetoothCompanyIdentiferMusik@222$kBluetoothCompanyIdentiferNEC@34$kBluetoothCompanyIdentiferNECLightning@149$kBluetoothCompanyIdentiferNautilus@244$kBluetoothCompanyIdentiferNewlogic@23$kBluetoothCompanyIdentiferNielsenKellerman@234$kBluetoothCompanyIdentiferNike@120$kBluetoothCompanyIdentiferNokiaMobilePhones@1$kBluetoothCompanyIdentiferNordicSemiconductor@89$kBluetoothCompanyIdentiferNorwoodSystems@46$kBluetoothCompanyIdentiferODMTechnology@150$kBluetoothCompanyIdentiferOTLDynamics@165$kBluetoothCompanyIdentiferOmegawave@174$kBluetoothCompanyIdentiferOnsetComputer@197$kBluetoothCompanyIdentiferOpenInterface@39$kBluetoothCompanyIdentiferPLUSLocationSystems@260$kBluetoothCompanyIdentiferPandaOcean@166$kBluetoothCompanyIdentiferParrotSA@67$kBluetoothCompanyIdentiferParthusTechnologies@14$kBluetoothCompanyIdentiferPassifSemiconductor@176$kBluetoothCompanyIdentiferPayPal@240$kBluetoothCompanyIdentiferPeterSystemtechnik@173$kBluetoothCompanyIdentiferPhilipsSemiconductor@37$kBluetoothCompanyIdentiferPlantronics@85$kBluetoothCompanyIdentiferPolarElectroEurope@209$kBluetoothCompanyIdentiferPolarElectroOY@107$kBluetoothCompanyIdentiferProctorAndGamble@220$kBluetoothCompanyIdentiferQualcomm@29$kBluetoothCompanyIdentiferQualcommConnectedExperiences@216$kBluetoothCompanyIdentiferQualcommInnovationCenter@184$kBluetoothCompanyIdentiferQualcommTechnologies@215$kBluetoothCompanyIdentiferQuintic@142$kBluetoothCompanyIdentiferQuupa@199$kBluetoothCompanyIdentiferRDAMicroelectronics@97$kBluetoothCompanyIdentiferRFCMicroDevices@40$kBluetoothCompanyIdentiferRTXTelecom@21$kBluetoothCompanyIdentiferRalinkTechnology@91$kBluetoothCompanyIdentiferRealtekSemiconductor@93$kBluetoothCompanyIdentiferRedMCommunications@50$kBluetoothCompanyIdentiferRenesasTechnology@54$kBluetoothCompanyIdentiferResearchInMotion@60$kBluetoothCompanyIdentiferRivieraWaves@96$kBluetoothCompanyIdentiferRohdeandSchwarz@25$kBluetoothCompanyIdentiferSPowerElectronics@187$kBluetoothCompanyIdentiferSRMedizinelektronik@161$kBluetoothCompanyIdentiferSTMicroelectronics@48$kBluetoothCompanyIdentiferSamsungElectronics@117$kBluetoothCompanyIdentiferSarisCyclingGroup@177$kBluetoothCompanyIdentiferSeersTechnology@125$kBluetoothCompanyIdentiferSeikoEpson@64$kBluetoothCompanyIdentiferSelflyBV@198$kBluetoothCompanyIdentiferSemilink@226$kBluetoothCompanyIdentiferSennheiserCommunications@130$kBluetoothCompanyIdentiferServerTechnology@235$kBluetoothCompanyIdentiferShangHaiSuperSmartElectronics@114$kBluetoothCompanyIdentiferShenzhenExcelsecuDataTechnology@193$kBluetoothCompanyIdentiferSiRFTechnology@80$kBluetoothCompanyIdentiferSigniaTechnologies@27$kBluetoothCompanyIdentiferSiliconWave@11$kBluetoothCompanyIdentiferSmartifier@245$kBluetoothCompanyIdentiferSocketCommunications@68$kBluetoothCompanyIdentiferSonyEricssonMobileCommunications@86$kBluetoothCompanyIdentiferSoundID@111$kBluetoothCompanyIdentiferSportsTrackingTechnologies@126$kBluetoothCompanyIdentiferStaccatoCommunications@77$kBluetoothCompanyIdentiferStalmartTechnology@191$kBluetoothCompanyIdentiferStanleyBlackAndDecker@254$kBluetoothCompanyIdentiferStarkeyLaboratories@186$kBluetoothCompanyIdentiferStickNFind@249$kBluetoothCompanyIdentiferStonestreetOne@94$kBluetoothCompanyIdentiferSummitDataCommunications@110$kBluetoothCompanyIdentiferSuuntoOy@159$kBluetoothCompanyIdentiferSwirlNetworks@181$kBluetoothCompanyIdentiferSymbolTechnologies@42$kBluetoothCompanyIdentiferSynopsys@49$kBluetoothCompanyIdentiferSystemsAndChips@62$kBluetoothCompanyIdentiferTTPCom@26$kBluetoothCompanyIdentiferTZeroTechnologies@81$kBluetoothCompanyIdentiferTaixingbangTechnology@211$kBluetoothCompanyIdentiferTelitWirelessSolutions@143$kBluetoothCompanyIdentiferTenovis@43$kBluetoothCompanyIdentiferTerax@56$kBluetoothCompanyIdentiferTexasInstruments@13$kBluetoothCompanyIdentiferThinkOptics@146$kBluetoothCompanyIdentiferTimeKeepingSystems@131$kBluetoothCompanyIdentiferTimexGroup@214$kBluetoothCompanyIdentiferTomTomInternational@256$kBluetoothCompanyIdentiferTopconPositioningSystems@139$kBluetoothCompanyIdentiferToshiba@4$kBluetoothCompanyIdentiferTransilica@24$kBluetoothCompanyIdentiferTreLab@183$kBluetoothCompanyIdentiferTypeProducts@255$kBluetoothCompanyIdentiferUbiquitousComputingTechnology@261$kBluetoothCompanyIdentiferUniversalElectriconics@147$kBluetoothCompanyIdentiferVSNTechnologies@247$kBluetoothCompanyIdentiferValenceTech@253$kBluetoothCompanyIdentiferVertu@162$kBluetoothCompanyIdentiferVisio@88$kBluetoothCompanyIdentiferVisteon@167$kBluetoothCompanyIdentiferVoyetraTurtleBeach@217$kBluetoothCompanyIdentiferVtrackSystems@233$kBluetoothCompanyIdentiferWavePlusTechnology@35$kBluetoothCompanyIdentiferWicentric@95$kBluetoothCompanyIdentiferWidcomm@17$kBluetoothCompanyIdentiferWilliamDemantHolding@263$kBluetoothCompanyIdentiferWitronTechnology@241$kBluetoothCompanyIdentiferWuXiVimicro@129$kBluetoothCompanyIdentiferZeevo@18$kBluetoothCompanyIdentiferZero1TV@152$kBluetoothCompanyIdentiferZomm@116$kBluetoothCompanyIdentiferZscanSoftware@141$kBluetoothCompanyIdentifertxtrGMBH@218$kBluetoothConnectionHandleNone@65535$kBluetoothDeviceClassMajorAny@*********$kBluetoothDeviceClassMajorAudio@4$kBluetoothDeviceClassMajorComputer@1$kBluetoothDeviceClassMajorHealth@9$kBluetoothDeviceClassMajorImaging@6$kBluetoothDeviceClassMajorLANAccessPoint@3$kBluetoothDeviceClassMajorMiscellaneous@0$kBluetoothDeviceClassMajorNone@**********$kBluetoothDeviceClassMajorPeripheral@5$kBluetoothDeviceClassMajorPhone@2$kBluetoothDeviceClassMajorToy@8$kBluetoothDeviceClassMajorUnclassified@31$kBluetoothDeviceClassMajorWearable@7$kBluetoothDeviceClassMinorAny@*********$kBluetoothDeviceClassMinorAudioCamcorder@13$kBluetoothDeviceClassMinorAudioCar@8$kBluetoothDeviceClassMinorAudioGamingToy@18$kBluetoothDeviceClassMinorAudioHandsFree@2$kBluetoothDeviceClassMinorAudioHeadphones@6$kBluetoothDeviceClassMinorAudioHeadset@1$kBluetoothDeviceClassMinorAudioHiFi@10$kBluetoothDeviceClassMinorAudioLoudspeaker@5$kBluetoothDeviceClassMinorAudioMicrophone@4$kBluetoothDeviceClassMinorAudioPortable@7$kBluetoothDeviceClassMinorAudioReserved1@3$kBluetoothDeviceClassMinorAudioReserved2@17$kBluetoothDeviceClassMinorAudioSetTopBox@9$kBluetoothDeviceClassMinorAudioUnclassified@0$kBluetoothDeviceClassMinorAudioVCR@11$kBluetoothDeviceClassMinorAudioVideoCamera@12$kBluetoothDeviceClassMinorAudioVideoConferencing@16$kBluetoothDeviceClassMinorAudioVideoDisplayAndLoudspeaker@15$kBluetoothDeviceClassMinorAudioVideoMonitor@14$kBluetoothDeviceClassMinorComputerDesktopWorkstation@1$kBluetoothDeviceClassMinorComputerHandheld@4$kBluetoothDeviceClassMinorComputerLaptop@3$kBluetoothDeviceClassMinorComputerPalmSized@5$kBluetoothDeviceClassMinorComputerServer@2$kBluetoothDeviceClassMinorComputerUnclassified@0$kBluetoothDeviceClassMinorComputerWearable@6$kBluetoothDeviceClassMinorHealthBloodPressureMonitor@1$kBluetoothDeviceClassMinorHealthDataDisplay@7$kBluetoothDeviceClassMinorHealthGlucoseMeter@4$kBluetoothDeviceClassMinorHealthHeartRateMonitor@6$kBluetoothDeviceClassMinorHealthPulseOximeter@5$kBluetoothDeviceClassMinorHealthScale@3$kBluetoothDeviceClassMinorHealthThermometer@2$kBluetoothDeviceClassMinorHealthUndefined@0$kBluetoothDeviceClassMinorImaging1Camera@8$kBluetoothDeviceClassMinorImaging1Display@4$kBluetoothDeviceClassMinorImaging1Printer@32$kBluetoothDeviceClassMinorImaging1Scanner@16$kBluetoothDeviceClassMinorImaging2Unclassified@0$kBluetoothDeviceClassMinorNone@**********$kBluetoothDeviceClassMinorPeripheral1Combo@48$kBluetoothDeviceClassMinorPeripheral1Keyboard@16$kBluetoothDeviceClassMinorPeripheral1Pointing@32$kBluetoothDeviceClassMinorPeripheral2AnyPointing@**********$kBluetoothDeviceClassMinorPeripheral2CardReader@6$kBluetoothDeviceClassMinorPeripheral2DigitalPen@7$kBluetoothDeviceClassMinorPeripheral2DigitizerTablet@5$kBluetoothDeviceClassMinorPeripheral2Gamepad@2$kBluetoothDeviceClassMinorPeripheral2GesturalInputDevice@9$kBluetoothDeviceClassMinorPeripheral2HandheldScanner@8$kBluetoothDeviceClassMinorPeripheral2Joystick@1$kBluetoothDeviceClassMinorPeripheral2RemoteControl@3$kBluetoothDeviceClassMinorPeripheral2SensingDevice@4$kBluetoothDeviceClassMinorPeripheral2Unclassified@0$kBluetoothDeviceClassMinorPhoneCellular@1$kBluetoothDeviceClassMinorPhoneCommonISDNAccess@5$kBluetoothDeviceClassMinorPhoneCordless@2$kBluetoothDeviceClassMinorPhoneSmartPhone@3$kBluetoothDeviceClassMinorPhoneUnclassified@0$kBluetoothDeviceClassMinorPhoneWiredModemOrVoiceGateway@4$kBluetoothDeviceClassMinorToyController@4$kBluetoothDeviceClassMinorToyDollActionFigure@3$kBluetoothDeviceClassMinorToyGame@5$kBluetoothDeviceClassMinorToyRobot@1$kBluetoothDeviceClassMinorToyVehicle@2$kBluetoothDeviceClassMinorWearableGlasses@5$kBluetoothDeviceClassMinorWearableHelmet@4$kBluetoothDeviceClassMinorWearableJacket@3$kBluetoothDeviceClassMinorWearablePager@2$kBluetoothDeviceClassMinorWearableWristWatch@1$kBluetoothDeviceNameMaxLength@248$kBluetoothDontAllowRoleSwitch@0$kBluetoothESCOConnection@2$kBluetoothEncryptionEnableBREDRAESCCM@2$kBluetoothEncryptionEnableBREDRE0@1$kBluetoothEncryptionEnableLEAESCCM@1$kBluetoothEncryptionEnableOff@0$kBluetoothEncryptionEnableOn@1$kBluetoothExtendedFeatureLEAndBREDRToSameDeviceHostMode@4$kBluetoothExtendedFeatureLESupportedHostMode@2$kBluetoothExtendedFeaturePing@2$kBluetoothExtendedFeatureReserved@4$kBluetoothExtendedFeatureSecureConnectionsControllerSupport@1$kBluetoothExtendedFeatureSimpleSecurePairingHostMode@1$kBluetoothExtendedFeatureSlotAvailabilityMask@16$kBluetoothExtendedFeatureTrainNudging@8$kBluetoothFeature3SlotEnhancedDataRateACLPackets@128$kBluetoothFeature3SlotEnhancedDataRateeSCOPackets@128$kBluetoothFeature5SlotEnhancedDataRateACLPackets@1$kBluetoothFeatureAFHCapableMaster@8$kBluetoothFeatureAFHCapablePeripheral@8$kBluetoothFeatureAFHCapableSlave@8$kBluetoothFeatureAFHClassificationMaster@16$kBluetoothFeatureAFHClassificationPeripheral@16$kBluetoothFeatureAFHClassificationSlave@16$kBluetoothFeatureALawLog@128$kBluetoothFeatureAbsenceMasks@4$kBluetoothFeatureAliasAuhentication@32$kBluetoothFeatureBroadcastEncryption@128$kBluetoothFeatureCVSD@1$kBluetoothFeatureChannelQuality@4$kBluetoothFeatureEV4Packets@1$kBluetoothFeatureEV5Packets@2$kBluetoothFeatureEncapsulatedPDU@16$kBluetoothFeatureEncryption@4$kBluetoothFeatureEnhancedDataRateACL2MbpsMode@2$kBluetoothFeatureEnhancedDataRateACL3MbpsMode@4$kBluetoothFeatureEnhancedDataRateeSCO2MbpsMode@32$kBluetoothFeatureEnhancedDataRateeSCO3MbpsMode@64$kBluetoothFeatureEnhancedInquiryScan@8$kBluetoothFeatureErroneousDataReporting@32$kBluetoothFeatureExtendedFeatures@128$kBluetoothFeatureExtendedInquiryResponse@1$kBluetoothFeatureExtendedSCOLink@128$kBluetoothFeatureFiveSlotPackets@2$kBluetoothFeatureFlowControlLagBit0@16$kBluetoothFeatureFlowControlLagBit1@32$kBluetoothFeatureFlowControlLagBit2@64$kBluetoothFeatureHV2Packets@16$kBluetoothFeatureHV3Packets@32$kBluetoothFeatureHoldMode@64$kBluetoothFeatureInquiryTransmissionPowerLevel@2$kBluetoothFeatureInterlacedInquiryScan@16$kBluetoothFeatureInterlacedPageScan@32$kBluetoothFeatureLESupportedController@64$kBluetoothFeatureLinkSupervisionTimeoutChangedEvent@1$kBluetoothFeatureNonFlushablePacketBoundaryFlag@64$kBluetoothFeaturePagingScheme@2$kBluetoothFeatureParkMode@1$kBluetoothFeaturePauseEncryption@4$kBluetoothFeaturePowerControl@4$kBluetoothFeaturePowerControlRequests@2$kBluetoothFeatureRSSI@2$kBluetoothFeatureRSSIWithInquiryResult@64$kBluetoothFeatureSCOLink@8$kBluetoothFeatureScatterMode@1$kBluetoothFeatureSecureSimplePairing@8$kBluetoothFeatureSlotOffset@8$kBluetoothFeatureSniffMode@128$kBluetoothFeatureSniffSubrating@2$kBluetoothFeatureSwitchRoles@32$kBluetoothFeatureThreeSlotPackets@1$kBluetoothFeatureTimingAccuracy@16$kBluetoothFeatureTransparentSCOData@8$kBluetoothFeatureULawLog@64$kBluetoothGAPAppearanceGenericBarcodeScanner@704$kBluetoothGAPAppearanceGenericBloodPressure@896$kBluetoothGAPAppearanceGenericClock@256$kBluetoothGAPAppearanceGenericComputer@128$kBluetoothGAPAppearanceGenericCycling@1152$kBluetoothGAPAppearanceGenericDisplay@320$kBluetoothGAPAppearanceGenericEyeGlasses@448$kBluetoothGAPAppearanceGenericGlucoseMeter@1024$kBluetoothGAPAppearanceGenericHeartrateSensor@832$kBluetoothGAPAppearanceGenericHumanInterfaceDevice@960$kBluetoothGAPAppearanceGenericKeyring@576$kBluetoothGAPAppearanceGenericMediaPlayer@640$kBluetoothGAPAppearanceGenericPhone@64$kBluetoothGAPAppearanceGenericRemoteControl@384$kBluetoothGAPAppearanceGenericRunningWalkingSensor@1088$kBluetoothGAPAppearanceGenericTag@512$kBluetoothGAPAppearanceGenericThermometer@768$kBluetoothGAPAppearanceGenericWatch@192$kBluetoothGAPAppearanceHumanInterfaceDeviceBarcodeScanner@968$kBluetoothGAPAppearanceHumanInterfaceDeviceCardReader@966$kBluetoothGAPAppearanceHumanInterfaceDeviceDigitalPen@967$kBluetoothGAPAppearanceHumanInterfaceDeviceDigitizerTablet@965$kBluetoothGAPAppearanceHumanInterfaceDeviceGamepad@964$kBluetoothGAPAppearanceHumanInterfaceDeviceJoystick@963$kBluetoothGAPAppearanceHumanInterfaceDeviceKeyboard@961$kBluetoothGAPAppearanceHumanInterfaceDeviceMouse@962$kBluetoothGAPAppearanceUnknown@0$kBluetoothGeneralInquiryAccessCodeIndex@0$kBluetoothGeneralInquiryAccessCodeLAPValue@10390323$kBluetoothHCICentralRole@0$kBluetoothHCICommandAMPTest@9$kBluetoothHCICommandAMPTestEnd@8$kBluetoothHCICommandAcceptConnectionRequest@9$kBluetoothHCICommandAcceptSniffRequest@49$kBluetoothHCICommandAcceptSynchronousConnectionRequest@41$kBluetoothHCICommandAddSCOConnection@7$kBluetoothHCICommandAuthenticationRequested@17$kBluetoothHCICommandChangeConnectionLinkKey@21$kBluetoothHCICommandChangeConnectionPacketType@15$kBluetoothHCICommandChangeLocalName@19$kBluetoothHCICommandCreateConnection@5$kBluetoothHCICommandCreateConnectionCancel@8$kBluetoothHCICommandCreateNewUnitKey@11$kBluetoothHCICommandDeleteReservedLTADDR@117$kBluetoothHCICommandDeleteStoredLinkKey@18$kBluetoothHCICommandDisconnect@6$kBluetoothHCICommandEnableAMPReceiverReports@7$kBluetoothHCICommandEnableDeviceUnderTestMode@3$kBluetoothHCICommandEnhancedAcceptSynchronousConnectionRequest@62$kBluetoothHCICommandEnhancedFlush@95$kBluetoothHCICommandEnhancedSetupSynchronousConnection@61$kBluetoothHCICommandExitParkMode@6$kBluetoothHCICommandExitPeriodicInquiryMode@4$kBluetoothHCICommandExitSniffMode@4$kBluetoothHCICommandFlowSpecification@16$kBluetoothHCICommandFlush@8$kBluetoothHCICommandGetLinkQuality@3$kBluetoothHCICommandGetMWSTransportLayerConfiguration@12$kBluetoothHCICommandGroupHostController@3$kBluetoothHCICommandGroupInformational@4$kBluetoothHCICommandGroupLinkControl@1$kBluetoothHCICommandGroupLinkPolicy@2$kBluetoothHCICommandGroupLogoTesting@62$kBluetoothHCICommandGroupLowEnergy@8$kBluetoothHCICommandGroupMax@64$kBluetoothHCICommandGroupNoOp@0$kBluetoothHCICommandGroupStatus@5$kBluetoothHCICommandGroupTesting@6$kBluetoothHCICommandGroupVendorSpecific@63$kBluetoothHCICommandHoldMode@1$kBluetoothHCICommandHostBufferSize@51$kBluetoothHCICommandHostNumberOfCompletedPackets@53$kBluetoothHCICommandIOCapabilityRequestNegativeReply@52$kBluetoothHCICommandIOCapabilityRequestReply@43$kBluetoothHCICommandInquiry@1$kBluetoothHCICommandInquiryCancel@2$kBluetoothHCICommandLEAddDeviceToPeriodicAdvertiserList@71$kBluetoothHCICommandLEAddDeviceToResolvingList@39$kBluetoothHCICommandLEAddDeviceToWhiteList@17$kBluetoothHCICommandLEClearAdvertisingSets@61$kBluetoothHCICommandLEClearPeriodicAdvertiserList@73$kBluetoothHCICommandLEClearResolvingList@41$kBluetoothHCICommandLEClearWhiteList@16$kBluetoothHCICommandLEConnectionUpdate@19$kBluetoothHCICommandLECreateConnection@13$kBluetoothHCICommandLECreateConnectionCancel@14$kBluetoothHCICommandLEEncrypt@23$kBluetoothHCICommandLEEnhancedReceiverTest@51$kBluetoothHCICommandLEEnhancedTransmitterTest@52$kBluetoothHCICommandLEExtendedCreateConnection@67$kBluetoothHCICommandLEGenerateDHKey@38$kBluetoothHCICommandLELongTermKeyRequestNegativeReply@27$kBluetoothHCICommandLELongTermKeyRequestReply@26$kBluetoothHCICommandLEPeriodicAdvertisingCreateSync@68$kBluetoothHCICommandLEPeriodicAdvertisingCreateSyncCancel@69$kBluetoothHCICommandLEPeriodicAdvertisingTerminateSync@70$kBluetoothHCICommandLERand@24$kBluetoothHCICommandLEReadAdvertisingChannelTxPower@7$kBluetoothHCICommandLEReadBufferSize@2$kBluetoothHCICommandLEReadChannelMap@21$kBluetoothHCICommandLEReadLocalP256PublicKey@37$kBluetoothHCICommandLEReadLocalResolvableAddress@44$kBluetoothHCICommandLEReadLocalSupportedFeatures@3$kBluetoothHCICommandLEReadMaximumAdvertisingDataLength@58$kBluetoothHCICommandLEReadMaximumDataLength@47$kBluetoothHCICommandLEReadNumberofSupportedAdvertisingSets@59$kBluetoothHCICommandLEReadPeerResolvableAddress@43$kBluetoothHCICommandLEReadPeriodicAdvertiserListSize@74$kBluetoothHCICommandLEReadPhy@48$kBluetoothHCICommandLEReadRFPathCompensation@76$kBluetoothHCICommandLEReadRemoteUsedFeatures@22$kBluetoothHCICommandLEReadResolvingListSize@42$kBluetoothHCICommandLEReadSuggestedDefaultDataLength@35$kBluetoothHCICommandLEReadSupportedStates@28$kBluetoothHCICommandLEReadTransmitPower@75$kBluetoothHCICommandLEReadWhiteListSize@15$kBluetoothHCICommandLEReceiverTest@29$kBluetoothHCICommandLERemoteConnectionParameterRequestNegativeReply@33$kBluetoothHCICommandLERemoteConnectionParameterRequestReply@32$kBluetoothHCICommandLERemoveAdvertisingSet@60$kBluetoothHCICommandLERemoveDeviceFromPeriodicAdvertiserList@72$kBluetoothHCICommandLERemoveDeviceFromResolvingList@40$kBluetoothHCICommandLERemoveDeviceFromWhiteList@18$kBluetoothHCICommandLESetAddressResolutionEnable@45$kBluetoothHCICommandLESetAdvertiseEnable@10$kBluetoothHCICommandLESetAdvertisingData@8$kBluetoothHCICommandLESetAdvertisingParameters@6$kBluetoothHCICommandLESetAdvertisingSetRandomAddress@53$kBluetoothHCICommandLESetDataLength@34$kBluetoothHCICommandLESetDefaultPhy@49$kBluetoothHCICommandLESetEventMask@1$kBluetoothHCICommandLESetExtendedAdvertisingData@55$kBluetoothHCICommandLESetExtendedAdvertisingEnableCommand@57$kBluetoothHCICommandLESetExtendedAdvertisingParameters@54$kBluetoothHCICommandLESetExtendedScanEnable@66$kBluetoothHCICommandLESetExtendedScanParameters@65$kBluetoothHCICommandLESetExtendedScanResponseData@56$kBluetoothHCICommandLESetHostChannelClassification@20$kBluetoothHCICommandLESetPeriodicAdvertisingData@63$kBluetoothHCICommandLESetPeriodicAdvertisingEnable@64$kBluetoothHCICommandLESetPeriodicAdvertisingParameters@62$kBluetoothHCICommandLESetPhy@50$kBluetoothHCICommandLESetPrivacyMode@78$kBluetoothHCICommandLESetRandomAddress@5$kBluetoothHCICommandLESetResolvablePrivateAddressTimeout@46$kBluetoothHCICommandLESetScanEnable@12$kBluetoothHCICommandLESetScanParameters@11$kBluetoothHCICommandLESetScanResponseData@9$kBluetoothHCICommandLEStartEncryption@25$kBluetoothHCICommandLETestEnd@31$kBluetoothHCICommandLETransmitterTest@30$kBluetoothHCICommandLEWriteRFPathCompensation@77$kBluetoothHCICommandLEWriteSuggestedDefaultDataLength@36$kBluetoothHCICommandLinkKeyRequestNegativeReply@12$kBluetoothHCICommandLinkKeyRequestReply@11$kBluetoothHCICommandMasterLinkKey@23$kBluetoothHCICommandMax@1023$kBluetoothHCICommandNoOp@0$kBluetoothHCICommandPINCodeRequestNegativeReply@14$kBluetoothHCICommandPINCodeRequestReply@13$kBluetoothHCICommandPacketHeaderSize@3$kBluetoothHCICommandPacketMaxDataSize@255$kBluetoothHCICommandParkMode@5$kBluetoothHCICommandPeriodicInquiryMode@3$kBluetoothHCICommandQoSSetup@7$kBluetoothHCICommandReadAFHChannelAssessmentMode@72$kBluetoothHCICommandReadAFHMappings@6$kBluetoothHCICommandReadAuthenticatedPayloadTimeout@123$kBluetoothHCICommandReadAuthenticationEnable@31$kBluetoothHCICommandReadAutomaticFlushTimeout@39$kBluetoothHCICommandReadBestEffortFlushTimeout@105$kBluetoothHCICommandReadBufferSize@5$kBluetoothHCICommandReadClassOfDevice@35$kBluetoothHCICommandReadClock@7$kBluetoothHCICommandReadClockOffset@31$kBluetoothHCICommandReadConnectionAcceptTimeout@21$kBluetoothHCICommandReadCountryCode@7$kBluetoothHCICommandReadCurrentIACLAP@57$kBluetoothHCICommandReadDataBlockSize@10$kBluetoothHCICommandReadDefaultErroneousDataReporting@90$kBluetoothHCICommandReadDefaultLinkPolicySettings@14$kBluetoothHCICommandReadDeviceAddress@9$kBluetoothHCICommandReadEncryptionKeySize@8$kBluetoothHCICommandReadEncryptionMode@33$kBluetoothHCICommandReadEnhancedTransmitPowerLevel@104$kBluetoothHCICommandReadExtendedInquiryLength@128$kBluetoothHCICommandReadExtendedInquiryResponse@81$kBluetoothHCICommandReadExtendedPageTimeout@126$kBluetoothHCICommandReadFailedContactCounter@1$kBluetoothHCICommandReadFlowControlMode@102$kBluetoothHCICommandReadHoldModeActivity@43$kBluetoothHCICommandReadInquiryMode@68$kBluetoothHCICommandReadInquiryResponseTransmitPower@88$kBluetoothHCICommandReadInquiryScanActivity@29$kBluetoothHCICommandReadInquiryScanType@66$kBluetoothHCICommandReadLEHostSupported@108$kBluetoothHCICommandReadLMPHandle@32$kBluetoothHCICommandReadLinkPolicySettings@12$kBluetoothHCICommandReadLinkSupervisionTimeout@54$kBluetoothHCICommandReadLocalAMPASSOC@10$kBluetoothHCICommandReadLocalAMPInfo@9$kBluetoothHCICommandReadLocalExtendedFeatures@4$kBluetoothHCICommandReadLocalName@20$kBluetoothHCICommandReadLocalOOBData@87$kBluetoothHCICommandReadLocalOOBExtendedData@125$kBluetoothHCICommandReadLocalSupportedCodecs@11$kBluetoothHCICommandReadLocalSupportedCommands@2$kBluetoothHCICommandReadLocalSupportedFeatures@3$kBluetoothHCICommandReadLocalVersionInformation@1$kBluetoothHCICommandReadLocationData@100$kBluetoothHCICommandReadLogicalLinkAcceptTimeout@97$kBluetoothHCICommandReadLoopbackMode@1$kBluetoothHCICommandReadNumberOfBroadcastRetransmissions@41$kBluetoothHCICommandReadNumberOfSupportedIAC@56$kBluetoothHCICommandReadPINType@9$kBluetoothHCICommandReadPageScanActivity@27$kBluetoothHCICommandReadPageScanMode@61$kBluetoothHCICommandReadPageScanPeriodMode@59$kBluetoothHCICommandReadPageScanType@70$kBluetoothHCICommandReadPageTimeout@23$kBluetoothHCICommandReadRSSI@5$kBluetoothHCICommandReadRemoteExtendedFeatures@28$kBluetoothHCICommandReadRemoteSupportedFeatures@27$kBluetoothHCICommandReadRemoteVersionInformation@29$kBluetoothHCICommandReadSCOFlowControlEnable@46$kBluetoothHCICommandReadScanEnable@25$kBluetoothHCICommandReadSecureConnectionsHostSupport@121$kBluetoothHCICommandReadSimplePairingMode@85$kBluetoothHCICommandReadStoredLinkKey@13$kBluetoothHCICommandReadSynchronizationTrainParameters@119$kBluetoothHCICommandReadTransmitPowerLevel@45$kBluetoothHCICommandReadVoiceSetting@37$kBluetoothHCICommandReceiveSynchronizationTrain@68$kBluetoothHCICommandRefreshEncryptionKey@83$kBluetoothHCICommandRejectConnectionRequest@10$kBluetoothHCICommandRejectSniffRequest@50$kBluetoothHCICommandRejectSynchronousConnectionRequest@42$kBluetoothHCICommandRemoteNameRequest@25$kBluetoothHCICommandRemoteNameRequestCancel@26$kBluetoothHCICommandRemoteOOBDataRequestNegativeReply@51$kBluetoothHCICommandRemoteOOBDataRequestReply@48$kBluetoothHCICommandRemoteOOBExtendedDataRequestReply@69$kBluetoothHCICommandReset@3$kBluetoothHCICommandResetFailedContactCounter@2$kBluetoothHCICommandRoleDiscovery@9$kBluetoothHCICommandSendKeypressNotification@96$kBluetoothHCICommandSetAFHClassification@63$kBluetoothHCICommandSetConnectionEncryption@19$kBluetoothHCICommandSetConnectionlessPeripheralBroadcast@65$kBluetoothHCICommandSetConnectionlessPeripheralBroadcastData@118$kBluetoothHCICommandSetConnectionlessPeripheralBroadcastReceive@66$kBluetoothHCICommandSetConnectionlessSlaveBroadcast@65$kBluetoothHCICommandSetConnectionlessSlaveBroadcastData@118$kBluetoothHCICommandSetConnectionlessSlaveBroadcastReceive@66$kBluetoothHCICommandSetEventFilter@5$kBluetoothHCICommandSetEventMask@1$kBluetoothHCICommandSetEventMaskPageTwo@99$kBluetoothHCICommandSetExternalFrameConfiguration@111$kBluetoothHCICommandSetHostControllerToHostFlowControl@49$kBluetoothHCICommandSetMWSChannelParameters@110$kBluetoothHCICommandSetMWSPATTERNConfiguration@115$kBluetoothHCICommandSetMWSScanFrequencyTable@114$kBluetoothHCICommandSetMWSSignaling@112$kBluetoothHCICommandSetMWSTransportLayer@113$kBluetoothHCICommandSetReservedLTADDR@116$kBluetoothHCICommandSetTriggeredClockCapture@13$kBluetoothHCICommandSetupSynchronousConnection@40$kBluetoothHCICommandShortRangeMode@107$kBluetoothHCICommandSniffMode@3$kBluetoothHCICommandSniffSubrating@17$kBluetoothHCICommandStartSynchronizationTrain@67$kBluetoothHCICommandSwitchRole@11$kBluetoothHCICommandTruncatedPage@63$kBluetoothHCICommandTruncatedPageCancel@64$kBluetoothHCICommandUserConfirmationRequestNegativeReply@45$kBluetoothHCICommandUserConfirmationRequestReply@44$kBluetoothHCICommandUserPasskeyRequestNegativeReply@47$kBluetoothHCICommandUserPasskeyRequestReply@46$kBluetoothHCICommandWriteAFHChannelAssessmentMode@73$kBluetoothHCICommandWriteAuthenticatedPayloadTimeout@124$kBluetoothHCICommandWriteAuthenticationEnable@32$kBluetoothHCICommandWriteAutomaticFlushTimeout@40$kBluetoothHCICommandWriteBestEffortFlushTimeout@106$kBluetoothHCICommandWriteClassOfDevice@36$kBluetoothHCICommandWriteConnectionAcceptTimeout@22$kBluetoothHCICommandWriteCurrentIACLAP@58$kBluetoothHCICommandWriteDefaultErroneousDataReporting@91$kBluetoothHCICommandWriteDefaultLinkPolicySettings@15$kBluetoothHCICommandWriteEncryptionMode@34$kBluetoothHCICommandWriteExtendedInquiryLength@129$kBluetoothHCICommandWriteExtendedInquiryResponse@82$kBluetoothHCICommandWriteExtendedPageTimeout@127$kBluetoothHCICommandWriteFlowControlMode@103$kBluetoothHCICommandWriteHoldModeActivity@44$kBluetoothHCICommandWriteInquiryMode@69$kBluetoothHCICommandWriteInquiryResponseTransmitPower@89$kBluetoothHCICommandWriteInquiryScanActivity@30$kBluetoothHCICommandWriteInquiryScanType@67$kBluetoothHCICommandWriteLEHostSupported@109$kBluetoothHCICommandWriteLinkPolicySettings@13$kBluetoothHCICommandWriteLinkSupervisionTimeout@55$kBluetoothHCICommandWriteLocationData@101$kBluetoothHCICommandWriteLogicalLinkAcceptTimeout@98$kBluetoothHCICommandWriteLoopbackMode@2$kBluetoothHCICommandWriteNumberOfBroadcastRetransmissions@42$kBluetoothHCICommandWritePINType@10$kBluetoothHCICommandWritePageScanActivity@28$kBluetoothHCICommandWritePageScanMode@62$kBluetoothHCICommandWritePageScanPeriodMode@60$kBluetoothHCICommandWritePageScanType@71$kBluetoothHCICommandWritePageTimeout@24$kBluetoothHCICommandWriteRemoteAMPASSOC@11$kBluetoothHCICommandWriteSCOFlowControlEnable@47$kBluetoothHCICommandWriteScanEnable@26$kBluetoothHCICommandWriteSecureConnectionsHostSupport@122$kBluetoothHCICommandWriteSimplePairingDebugMode@4$kBluetoothHCICommandWriteSimplePairingMode@86$kBluetoothHCICommandWriteStoredLinkKey@17$kBluetoothHCICommandWriteSynchronizationTrainParameters@120$kBluetoothHCICommandWriteVoiceSetting@38$kBluetoothHCIDataPacketHeaderSize@4$kBluetoothHCIDataPacketMaxDataSize@65535$kBluetoothHCIErroneousDataReportingDisabled@0$kBluetoothHCIErroneousDataReportingEnabled@1$kBluetoothHCIErroneousDataReportingReservedEnd@255$kBluetoothHCIErroneousDataReportingReservedStart@2$kBluetoothHCIErrorACLConnectionAlreadyExists@11$kBluetoothHCIErrorAuthenticationFailure@5$kBluetoothHCIErrorChannelClassificationNotSupported@46$kBluetoothHCIErrorCoarseClockAdjustmentRejected@64$kBluetoothHCIErrorCommandDisallowed@12$kBluetoothHCIErrorConnectionFailedToBeEstablished@62$kBluetoothHCIErrorConnectionRejectedDueToNoSuitableChannelFound@57$kBluetoothHCIErrorConnectionTerminatedByLocalHost@22$kBluetoothHCIErrorConnectionTerminatedDueToMICFailure@61$kBluetoothHCIErrorConnectionTimeout@8$kBluetoothHCIErrorControllerBusy@58$kBluetoothHCIErrorDifferentTransactionCollision@42$kBluetoothHCIErrorDirectedAdvertisingTimeout@60$kBluetoothHCIErrorEncryptionModeNotAcceptable@37$kBluetoothHCIErrorExtendedInquiryResponseTooLarge@54$kBluetoothHCIErrorHardwareFailure@3$kBluetoothHCIErrorHostBusyPairing@56$kBluetoothHCIErrorHostRejectedLimitedResources@13$kBluetoothHCIErrorHostRejectedRemoteDeviceIsPersonal@15$kBluetoothHCIErrorHostRejectedSecurityReasons@14$kBluetoothHCIErrorHostRejectedUnacceptableDeviceAddress@15$kBluetoothHCIErrorHostTimeout@16$kBluetoothHCIErrorInstantPassed@40$kBluetoothHCIErrorInsufficientSecurity@47$kBluetoothHCIErrorInvalidHCICommandParameters@18$kBluetoothHCIErrorInvalidLMPParameters@30$kBluetoothHCIErrorKeyMissing@6$kBluetoothHCIErrorLMPErrorTransactionCollision@35$kBluetoothHCIErrorLMPPDUNotAllowed@36$kBluetoothHCIErrorLMPResponseTimeout@34$kBluetoothHCIErrorMACConnectionFailed@63$kBluetoothHCIErrorMax@64$kBluetoothHCIErrorMaxNumberOfConnections@9$kBluetoothHCIErrorMaxNumberOfSCOConnectionsToADevice@10$kBluetoothHCIErrorMemoryFull@7$kBluetoothHCIErrorNoConnection@2$kBluetoothHCIErrorOtherEndTerminatedConnectionAboutToPowerOff@21$kBluetoothHCIErrorOtherEndTerminatedConnectionLowResources@20$kBluetoothHCIErrorOtherEndTerminatedConnectionUserEnded@19$kBluetoothHCIErrorPageTimeout@4$kBluetoothHCIErrorPairingNotAllowed@24$kBluetoothHCIErrorPairingWithUnitKeyNotSupported@41$kBluetoothHCIErrorParameterOutOfMandatoryRange@48$kBluetoothHCIErrorPowerIsOFF@65$kBluetoothHCIErrorQoSNotSupported@39$kBluetoothHCIErrorQoSRejected@45$kBluetoothHCIErrorQoSUnacceptableParameter@44$kBluetoothHCIErrorRepeatedAttempts@23$kBluetoothHCIErrorReservedSlotViolation@52$kBluetoothHCIErrorRoleChangeNotAllowed@33$kBluetoothHCIErrorRoleSwitchFailed@53$kBluetoothHCIErrorRoleSwitchPending@49$kBluetoothHCIErrorSCOAirModeRejected@29$kBluetoothHCIErrorSCOIntervalRejected@28$kBluetoothHCIErrorSCOOffsetRejected@27$kBluetoothHCIErrorSecureSimplePairingNotSupportedByHost@55$kBluetoothHCIErrorSuccess@0$kBluetoothHCIErrorUnacceptableConnectionInterval@59$kBluetoothHCIErrorUnitKeyUsed@38$kBluetoothHCIErrorUnknownHCICommand@1$kBluetoothHCIErrorUnknownLMPPDU@25$kBluetoothHCIErrorUnspecifiedError@31$kBluetoothHCIErrorUnsupportedFeatureOrParameterValue@17$kBluetoothHCIErrorUnsupportedLMPParameterValue@32$kBluetoothHCIErrorUnsupportedRemoteFeature@26$kBluetoothHCIEventAMPReceiverReport@75$kBluetoothHCIEventAMPStartTest@73$kBluetoothHCIEventAMPStatusChange@77$kBluetoothHCIEventAMPTestEnd@74$kBluetoothHCIEventAuthenticationComplete@6$kBluetoothHCIEventChangeConnectionLinkKeyComplete@9$kBluetoothHCIEventChannelSelected@65$kBluetoothHCIEventCommandComplete@14$kBluetoothHCIEventCommandStatus@15$kBluetoothHCIEventConnectionComplete@3$kBluetoothHCIEventConnectionPacketType@29$kBluetoothHCIEventConnectionRequest@4$kBluetoothHCIEventDataBufferOverflow@26$kBluetoothHCIEventDisconnectionComplete@5$kBluetoothHCIEventDisconnectionLogicalLinkComplete@70$kBluetoothHCIEventDisconnectionPhysicalLinkComplete@66$kBluetoothHCIEventEncryptionChange@8$kBluetoothHCIEventEncryptionKeyRefreshComplete@48$kBluetoothHCIEventEnhancedFlushComplete@57$kBluetoothHCIEventExtendedInquiryResult@47$kBluetoothHCIEventFlowSpecModifyComplete@71$kBluetoothHCIEventFlowSpecificationComplete@33$kBluetoothHCIEventFlushOccurred@17$kBluetoothHCIEventHardwareError@16$kBluetoothHCIEventIOCapabilityRequest@49$kBluetoothHCIEventIOCapabilityResponse@50$kBluetoothHCIEventInquiryComplete@1$kBluetoothHCIEventInquiryResult@2$kBluetoothHCIEventInquiryResultWithRSSI@34$kBluetoothHCIEventKeypressNotification@60$kBluetoothHCIEventLEMetaEvent@62$kBluetoothHCIEventLinkKeyNotification@24$kBluetoothHCIEventLinkKeyRequest@23$kBluetoothHCIEventLinkSupervisionTimeoutChanged@56$kBluetoothHCIEventLogicalLinkComplete@69$kBluetoothHCIEventLogoTesting@254$kBluetoothHCIEventLoopbackCommand@25$kBluetoothHCIEventMaskAll@4294967295$kBluetoothHCIEventMaskAll64Bit@18446744073709551615$kBluetoothHCIEventMaskAuthenticationComplete@32$kBluetoothHCIEventMaskChangeConnectionLinkKeyComplete@256$kBluetoothHCIEventMaskCommandComplete@8192$kBluetoothHCIEventMaskCommandStatus@16384$kBluetoothHCIEventMaskConnectionComplete@4$kBluetoothHCIEventMaskConnectionPacketTypeChanged@268435456$kBluetoothHCIEventMaskConnectionRequest@8$kBluetoothHCIEventMaskDataBufferOverflow@33554432$kBluetoothHCIEventMaskDefault@4294967295$kBluetoothHCIEventMaskDefault64Bit@35184372088831$kBluetoothHCIEventMaskDisconnectionComplete@16$kBluetoothHCIEventMaskEncryptionChange@128$kBluetoothHCIEventMaskEncryptionChangeEvent@128$kBluetoothHCIEventMaskEncryptionKeyRefreshCompleteEvent@140737488355328$kBluetoothHCIEventMaskEnhancedFlushCompleteEvent@72057594037927936$kBluetoothHCIEventMaskExtendedInquiryResultEvent@70368744177664$kBluetoothHCIEventMaskFlowSpecificationCompleteEvent@4294967296$kBluetoothHCIEventMaskFlushOccurred@65536$kBluetoothHCIEventMaskHardwareError@32768$kBluetoothHCIEventMaskIOCapabilityRequestEvent@281474976710656$kBluetoothHCIEventMaskIOCapabilityRequestReplyEvent@562949953421312$kBluetoothHCIEventMaskInquiryComplete@1$kBluetoothHCIEventMaskInquiryResult@2$kBluetoothHCIEventMaskInquiryResultWithRSSIEvent@8589934592$kBluetoothHCIEventMaskKeypressNotificationEvent@576460752303423488$kBluetoothHCIEventMaskLEDefault64Bit@31$kBluetoothHCIEventMaskLEMetaEvent@2305843009213693952$kBluetoothHCIEventMaskLinkKeyNotification@8388608$kBluetoothHCIEventMaskLinkKeyRequest@4194304$kBluetoothHCIEventMaskLinkSupervisionTimeoutChangedEvent@36028797018963968$kBluetoothHCIEventMaskLoopbackCommand@16777216$kBluetoothHCIEventMaskMasterLinkKeyComplete@512$kBluetoothHCIEventMaskMaxSlotsChange@67108864$kBluetoothHCIEventMaskModeChange@524288$kBluetoothHCIEventMaskNone@0$kBluetoothHCIEventMaskNumberOfCompletedPackets@262144$kBluetoothHCIEventMaskPINCodeRequest@2097152$kBluetoothHCIEventMaskPageScanModeChange@1073741824$kBluetoothHCIEventMaskPageScanRepetitionModeChange@2147483648$kBluetoothHCIEventMaskQoSSetupComplete@4096$kBluetoothHCIEventMaskQoSViolation@536870912$kBluetoothHCIEventMaskReadClockOffsetComplete@134217728$kBluetoothHCIEventMaskReadRemoteExtendedFeaturesCompleteEvent@17179869184$kBluetoothHCIEventMaskReadRemoteSupportedFeaturesComplete@1024$kBluetoothHCIEventMaskReadRemoteVersionInformationComplete@2048$kBluetoothHCIEventMaskRemoteHostSupportedFeaturesNotificationEvent@1152921504606846976$kBluetoothHCIEventMaskRemoteNameRequestComplete@64$kBluetoothHCIEventMaskRemoteOOBDataRequestEvent@4503599627370496$kBluetoothHCIEventMaskReturnLinkKeys@1048576$kBluetoothHCIEventMaskRoleChange@131072$kBluetoothHCIEventMaskSimplePairingCompleteEvent@9007199254740992$kBluetoothHCIEventMaskSniffSubratingEvent@35184372088832$kBluetoothHCIEventMaskSynchronousConnectionChangedEvent@17592186044416$kBluetoothHCIEventMaskSynchronousConnectionCompleteEvent@8796093022208$kBluetoothHCIEventMaskUserConfirmationRequestEvent@1125899906842624$kBluetoothHCIEventMaskUserPasskeyNotificationEvent@288230376151711744$kBluetoothHCIEventMaskUserPasskeyRequestEvent@2251799813685248$kBluetoothHCIEventMasterLinkKeyComplete@10$kBluetoothHCIEventMaxSlotsChange@27$kBluetoothHCIEventModeChange@20$kBluetoothHCIEventNumberOfCompletedDataBlocks@72$kBluetoothHCIEventNumberOfCompletedPackets@19$kBluetoothHCIEventPINCodeRequest@22$kBluetoothHCIEventPacketHeaderSize@2$kBluetoothHCIEventPacketMaxDataSize@255$kBluetoothHCIEventPageScanModeChange@31$kBluetoothHCIEventPageScanRepetitionModeChange@32$kBluetoothHCIEventPhysicalLinkComplete@64$kBluetoothHCIEventPhysicalLinkLossEarlyWarning@67$kBluetoothHCIEventPhysicalLinkRecovery@68$kBluetoothHCIEventQoSSetupComplete@13$kBluetoothHCIEventQoSViolation@30$kBluetoothHCIEventReadClockOffsetComplete@28$kBluetoothHCIEventReadRemoteExtendedFeaturesComplete@35$kBluetoothHCIEventReadRemoteSupportedFeaturesComplete@11$kBluetoothHCIEventReadRemoteVersionInformationComplete@12$kBluetoothHCIEventRemoteHostSupportedFeaturesNotification@61$kBluetoothHCIEventRemoteNameRequestComplete@7$kBluetoothHCIEventRemoteOOBDataRequest@53$kBluetoothHCIEventReturnLinkKeys@21$kBluetoothHCIEventRoleChange@18$kBluetoothHCIEventShortRangeModeChangeComplete@76$kBluetoothHCIEventSimplePairingComplete@54$kBluetoothHCIEventSniffSubrating@46$kBluetoothHCIEventSynchronousConnectionChanged@45$kBluetoothHCIEventSynchronousConnectionComplete@44$kBluetoothHCIEventUserConfirmationRequest@51$kBluetoothHCIEventUserPasskeyNotification@59$kBluetoothHCIEventUserPasskeyRequest@52$kBluetoothHCIEventVendorSpecific@255$kBluetoothHCIEvnetMaskEnhancedFlushCompleteEvent@72057594037927936$kBluetoothHCIEvnetMaskLinkSupervisionTimeoutChangedEvent@36028797018963968$kBluetoothHCIExtendedInquiryResponseDataType128BitServiceClassUUIDsCompleteList@7$kBluetoothHCIExtendedInquiryResponseDataType128BitServiceClassUUIDsWithMoreAvailable@6$kBluetoothHCIExtendedInquiryResponseDataType16BitServiceClassUUIDsCompleteList@3$kBluetoothHCIExtendedInquiryResponseDataType16BitServiceClassUUIDsWithMoreAvailable@2$kBluetoothHCIExtendedInquiryResponseDataType32BitServiceClassUUIDsCompleteList@5$kBluetoothHCIExtendedInquiryResponseDataType32BitServiceClassUUIDsWithMoreAvailable@4$kBluetoothHCIExtendedInquiryResponseDataType3DInformationData@61$kBluetoothHCIExtendedInquiryResponseDataTypeAdvertisingInterval@26$kBluetoothHCIExtendedInquiryResponseDataTypeAppearance@25$kBluetoothHCIExtendedInquiryResponseDataTypeCompleteLocalName@9$kBluetoothHCIExtendedInquiryResponseDataTypeCsisRsiData@46$kBluetoothHCIExtendedInquiryResponseDataTypeDeviceID@16$kBluetoothHCIExtendedInquiryResponseDataTypeFlags@1$kBluetoothHCIExtendedInquiryResponseDataTypeIndoorPositioning@37$kBluetoothHCIExtendedInquiryResponseDataTypeLEBluetoothDeviceAddress@27$kBluetoothHCIExtendedInquiryResponseDataTypeLERole@28$kBluetoothHCIExtendedInquiryResponseDataTypeManufacturerSpecificData@255$kBluetoothHCIExtendedInquiryResponseDataTypePeripheralConnectionIntervalRange@18$kBluetoothHCIExtendedInquiryResponseDataTypePublicTargetAddress@23$kBluetoothHCIExtendedInquiryResponseDataTypeRandomTargetAddress@24$kBluetoothHCIExtendedInquiryResponseDataTypeSSPOOBClassOfDevice@13$kBluetoothHCIExtendedInquiryResponseDataTypeSSPOOBSimplePairingHashC@14$kBluetoothHCIExtendedInquiryResponseDataTypeSSPOOBSimplePairingRandomizerR@15$kBluetoothHCIExtendedInquiryResponseDataTypeSecureConnectionsConfirmationValue@34$kBluetoothHCIExtendedInquiryResponseDataTypeSecureConnectionsRandomValue@35$kBluetoothHCIExtendedInquiryResponseDataTypeSecurityManagerOOBFlags@17$kBluetoothHCIExtendedInquiryResponseDataTypeSecurityManagerTKValue@16$kBluetoothHCIExtendedInquiryResponseDataTypeServiceData@22$kBluetoothHCIExtendedInquiryResponseDataTypeServiceData128BitUUID@33$kBluetoothHCIExtendedInquiryResponseDataTypeServiceData32BitUUID@32$kBluetoothHCIExtendedInquiryResponseDataTypeServiceSolicitation128BitUUIDs@21$kBluetoothHCIExtendedInquiryResponseDataTypeServiceSolicitation16BitUUIDs@20$kBluetoothHCIExtendedInquiryResponseDataTypeServiceSolicitation32BitUUIDs@31$kBluetoothHCIExtendedInquiryResponseDataTypeShortenedLocalName@8$kBluetoothHCIExtendedInquiryResponseDataTypeSimplePairingHash@29$kBluetoothHCIExtendedInquiryResponseDataTypeSimplePairingRandomizer@30$kBluetoothHCIExtendedInquiryResponseDataTypeSlaveConnectionIntervalRange@18$kBluetoothHCIExtendedInquiryResponseDataTypeTransmitPowerLevel@10$kBluetoothHCIExtendedInquiryResponseDataTypeTransportDiscoveryData@38$kBluetoothHCIExtendedInquiryResponseDataTypeURI@36$kBluetoothHCIFECNotRequired@1$kBluetoothHCIFECRequired@0$kBluetoothHCIInquiryModeResultFormatStandard@0$kBluetoothHCIInquiryModeResultFormatWithRSSI@1$kBluetoothHCIInquiryModeResultFormatWithRSSIOrExtendedInquiryResultFormat@2$kBluetoothHCIInquiryResultsMaxResults@50$kBluetoothHCIInquiryScanTypeInterlaced@1$kBluetoothHCIInquiryScanTypeReservedEnd@255$kBluetoothHCIInquiryScanTypeReservedStart@2$kBluetoothHCIInquiryScanTypeStandard@0$kBluetoothHCILoopbackModeLocal@1$kBluetoothHCILoopbackModeOff@0$kBluetoothHCILoopbackModeRemote@2$kBluetoothHCIMasterRole@0$kBluetoothHCIMaxCommandPacketSize@258$kBluetoothHCIMaxDataPacketSize@65539$kBluetoothHCIMaxEventPacketSize@257$kBluetoothHCIOpCodeNoOp@0$kBluetoothHCIPageScanTypeInterlaced@1$kBluetoothHCIPageScanTypeReservedEnd@255$kBluetoothHCIPageScanTypeReservedStart@2$kBluetoothHCIPageScanTypeStandard@0$kBluetoothHCIPeripheralRole@1$kBluetoothHCIPowerStateOFF@0$kBluetoothHCIPowerStateON@1$kBluetoothHCIPowerStateUnintialized@255$kBluetoothHCISimplePairingDebugModeDisabled@0$kBluetoothHCISimplePairingDebugModeEnabled@1$kBluetoothHCISimplePairingModeEnabled@1$kBluetoothHCISimplePairingModeNotSet@0$kBluetoothHCISlaveRole@1$kBluetoothHCISubEventLEAdvertisingReport@2$kBluetoothHCISubEventLEAdvertisingSetTerminated@18$kBluetoothHCISubEventLEChannelSelectionAlgorithm@20$kBluetoothHCISubEventLEConnectionComplete@1$kBluetoothHCISubEventLEConnectionUpdateComplete@3$kBluetoothHCISubEventLEDataLengthChange@7$kBluetoothHCISubEventLEDirectAdvertisingReport@11$kBluetoothHCISubEventLEEnhancedConnectionComplete@10$kBluetoothHCISubEventLEExtendedAdvertising@13$kBluetoothHCISubEventLEGenerateDHKeyComplete@9$kBluetoothHCISubEventLELongTermKeyRequest@5$kBluetoothHCISubEventLEPeriodicAdvertisingReport@15$kBluetoothHCISubEventLEPeriodicAdvertisingSyncEstablished@14$kBluetoothHCISubEventLEPeriodicAdvertisingSyncLost@16$kBluetoothHCISubEventLEPhyUpdateComplete@12$kBluetoothHCISubEventLEReadLocalP256PublicKeyComplete@8$kBluetoothHCISubEventLEReadRemoteUsedFeaturesComplete@4$kBluetoothHCISubEventLERemoteConnectionParameterRequest@6$kBluetoothHCISubEventLEScanRequestReceived@19$kBluetoothHCISubEventLEScanTimeout@17$kBluetoothHCITransportUSBClassCode@224$kBluetoothHCITransportUSBProtocolCode@1$kBluetoothHCITransportUSBSubClassCode@1$kBluetoothHCIVersionCoreSpecification1_0b@0$kBluetoothHCIVersionCoreSpecification1_1@1$kBluetoothHCIVersionCoreSpecification1_2@2$kBluetoothHCIVersionCoreSpecification2_0EDR@3$kBluetoothHCIVersionCoreSpecification2_1EDR@4$kBluetoothHCIVersionCoreSpecification3_0HS@5$kBluetoothHCIVersionCoreSpecification4_0@6$kBluetoothHCIVersionCoreSpecification4_1@7$kBluetoothHCIVersionCoreSpecification4_2@8$kBluetoothHCIVersionCoreSpecification5_0@9$kBluetoothHCIVersionCoreSpecification5_1@10$kBluetoothHCIVersionCoreSpecification5_2@11$kBluetoothHCIVersionCoreSpecification5_3@12$kBluetoothKeyFlagSemiPermanent@0$kBluetoothKeyFlagTemporary@1$kBluetoothKeyTypeAuthenticatedCombination@5$kBluetoothKeyTypeAuthenticatedCombinationP256@8$kBluetoothKeyTypeChangedCombination@6$kBluetoothKeyTypeCombination@0$kBluetoothKeyTypeDebugCombination@3$kBluetoothKeyTypeLocalUnit@1$kBluetoothKeyTypeRemoteUnit@2$kBluetoothKeyTypeUnauthenticatedCombination@4$kBluetoothKeyTypeUnauthenticatedCombinationP256@7$kBluetoothKeypressNotificationTypePasskeyCleared@3$kBluetoothKeypressNotificationTypePasskeyDigitEntered@1$kBluetoothKeypressNotificationTypePasskeyDigitErased@2$kBluetoothKeypressNotificationTypePasskeyEntryCompleted@4$kBluetoothKeypressNotificationTypePasskeyEntryStarted@0$kBluetoothL2CAPChannelAMPManagerProtocol@3$kBluetoothL2CAPChannelAMPTestManager@63$kBluetoothL2CAPChannelAttributeProtocol@4$kBluetoothL2CAPChannelBREDRSecurityManager@7$kBluetoothL2CAPChannelConnectionLessData@2$kBluetoothL2CAPChannelDynamicEnd@65535$kBluetoothL2CAPChannelDynamicStart@64$kBluetoothL2CAPChannelEnd@65535$kBluetoothL2CAPChannelLEAP@42$kBluetoothL2CAPChannelLEAS@43$kBluetoothL2CAPChannelLESignalling@5$kBluetoothL2CAPChannelMagicPairing@48$kBluetoothL2CAPChannelMagnet@58$kBluetoothL2CAPChannelNull@0$kBluetoothL2CAPChannelReservedEnd@62$kBluetoothL2CAPChannelReservedStart@8$kBluetoothL2CAPChannelSecurityManager@6$kBluetoothL2CAPChannelSignalling@1$kBluetoothL2CAPCommandCodeCommandReject@1$kBluetoothL2CAPCommandCodeConfigureRequest@4$kBluetoothL2CAPCommandCodeConfigureResponse@5$kBluetoothL2CAPCommandCodeConnectionParameterUpdateRequest@18$kBluetoothL2CAPCommandCodeConnectionParameterUpdateResponse@19$kBluetoothL2CAPCommandCodeConnectionRequest@2$kBluetoothL2CAPCommandCodeConnectionResponse@3$kBluetoothL2CAPCommandCodeCreateChannelRequest@12$kBluetoothL2CAPCommandCodeCreateChannelResponse@13$kBluetoothL2CAPCommandCodeDisconnectionRequest@6$kBluetoothL2CAPCommandCodeDisconnectionResponse@7$kBluetoothL2CAPCommandCodeEchoRequest@8$kBluetoothL2CAPCommandCodeEchoResponse@9$kBluetoothL2CAPCommandCodeInformationRequest@10$kBluetoothL2CAPCommandCodeInformationResponse@11$kBluetoothL2CAPCommandCodeLECreditBasedConnectionRequest@20$kBluetoothL2CAPCommandCodeLECreditBasedConnectionResponse@21$kBluetoothL2CAPCommandCodeLEFlowControlCredit@22$kBluetoothL2CAPCommandCodeMoveChannelConfirmation@16$kBluetoothL2CAPCommandCodeMoveChannelConfirmationResponse@17$kBluetoothL2CAPCommandCodeMoveChannelRequest@14$kBluetoothL2CAPCommandCodeMoveChannelResponse@15$kBluetoothL2CAPCommandCodeReserved@0$kBluetoothL2CAPCommandRejectReasonCommandNotUnderstood@0$kBluetoothL2CAPCommandRejectReasonInvalidCIDInRequest@2$kBluetoothL2CAPCommandRejectReasonSignallingMTUExceeded@1$kBluetoothL2CAPConfigurationBasicL2CAPModeFlag@0$kBluetoothL2CAPConfigurationEnhancedRetransmissionMode@3$kBluetoothL2CAPConfigurationFlowControlModeFlag@2$kBluetoothL2CAPConfigurationOptionExtendedFlowSpecification@6$kBluetoothL2CAPConfigurationOptionExtendedWindowSize@7$kBluetoothL2CAPConfigurationOptionFlushTimeout@2$kBluetoothL2CAPConfigurationOptionFlushTimeoutLength@2$kBluetoothL2CAPConfigurationOptionFrameCheckSequence@5$kBluetoothL2CAPConfigurationOptionMTU@1$kBluetoothL2CAPConfigurationOptionMTULength@2$kBluetoothL2CAPConfigurationOptionQoS@3$kBluetoothL2CAPConfigurationOptionQoSLength@22$kBluetoothL2CAPConfigurationOptionRetransmissionAndFlowControl@4$kBluetoothL2CAPConfigurationOptionRetransmissionAndFlowControlLength@9$kBluetoothL2CAPConfigurationResultRejected@2$kBluetoothL2CAPConfigurationResultSuccess@0$kBluetoothL2CAPConfigurationResultUnacceptableParams@1$kBluetoothL2CAPConfigurationResultUnknownOptions@3$kBluetoothL2CAPConfigurationRetransmissionModeFlag@1$kBluetoothL2CAPConfigurationStreamingMode@4$kBluetoothL2CAPConnectionResultPending@1$kBluetoothL2CAPConnectionResultRefusedInvalidSourceCID@6$kBluetoothL2CAPConnectionResultRefusedNoResources@4$kBluetoothL2CAPConnectionResultRefusedPSMNotSupported@2$kBluetoothL2CAPConnectionResultRefusedReserved@5$kBluetoothL2CAPConnectionResultRefusedSecurityBlock@3$kBluetoothL2CAPConnectionResultRefusedSourceCIDAlreadyAllocated@7$kBluetoothL2CAPConnectionResultSuccessful@0$kBluetoothL2CAPConnectionStatusAuthenticationPending@1$kBluetoothL2CAPConnectionStatusAuthorizationPending@2$kBluetoothL2CAPConnectionStatusNoInfoAvailable@0$kBluetoothL2CAPFlushTimeoutDefault@65535$kBluetoothL2CAPFlushTimeoutForever@65535$kBluetoothL2CAPFlushTimeoutImmediate@1$kBluetoothL2CAPFlushTimeoutUseExisting@0$kBluetoothL2CAPInfoTypeMaxConnectionlessMTUSize@1$kBluetoothL2CAPInformationBidirectionalQoS@4$kBluetoothL2CAPInformationEnhancedRetransmissionMode@8$kBluetoothL2CAPInformationExtendedFlowSpecification@64$kBluetoothL2CAPInformationExtendedWindowSize@256$kBluetoothL2CAPInformationFCSOption@32$kBluetoothL2CAPInformationFixedChannels@128$kBluetoothL2CAPInformationFlowControlMode@1$kBluetoothL2CAPInformationNoExtendedFeatures@0$kBluetoothL2CAPInformationResultNotSupported@1$kBluetoothL2CAPInformationResultSuccess@0$kBluetoothL2CAPInformationRetransmissionMode@2$kBluetoothL2CAPInformationStreamingMode@16$kBluetoothL2CAPInformationTypeConnectionlessMTU@1$kBluetoothL2CAPInformationTypeExtendedFeatures@2$kBluetoothL2CAPInformationTypeFixedChannelsSupported@3$kBluetoothL2CAPMTUDefault@1017$kBluetoothL2CAPMTULowEnergyDefault@27$kBluetoothL2CAPMTULowEnergyMax@251$kBluetoothL2CAPMTUMaximum@65535$kBluetoothL2CAPMTUMinimum@48$kBluetoothL2CAPMTUSIG@48$kBluetoothL2CAPMTUStart@32767$kBluetoothL2CAPMaxPacketSize@65535$kBluetoothL2CAPPSMAACP@4097$kBluetoothL2CAPPSMATT@31$kBluetoothL2CAPPSMAVCTP@23$kBluetoothL2CAPPSMAVCTP_Browsing@27$kBluetoothL2CAPPSMAVDTP@25$kBluetoothL2CAPPSMBNEP@15$kBluetoothL2CAPPSMD2D@4111$kBluetoothL2CAPPSMDynamicEnd@65535$kBluetoothL2CAPPSMDynamicStart@4097$kBluetoothL2CAPPSMHIDControl@17$kBluetoothL2CAPPSMHIDInterrupt@19$kBluetoothL2CAPPSMNone@0$kBluetoothL2CAPPSMRFCOMM@3$kBluetoothL2CAPPSMReservedEnd@4096$kBluetoothL2CAPPSMReservedStart@1$kBluetoothL2CAPPSMSDP@1$kBluetoothL2CAPPSMTCS_BIN@5$kBluetoothL2CAPPSMTCS_BIN_Cordless@7$kBluetoothL2CAPPSMUID_C_Plane@29$kBluetoothL2CAPPacketHeaderSize@4$kBluetoothL2CAPQoSDelayVariationDefault@4294967295$kBluetoothL2CAPQoSFlagsDefault@0$kBluetoothL2CAPQoSLatencyDefault@4294967295$kBluetoothL2CAPQoSPeakBandwidthDefault@0$kBluetoothL2CAPQoSTokenBucketSizeDefault@0$kBluetoothL2CAPQoSTokenRateDefault@0$kBluetoothL2CAPQoSTypeBestEffort@1$kBluetoothL2CAPQoSTypeDefault@1$kBluetoothL2CAPQoSTypeGuaranteed@2$kBluetoothL2CAPQoSTypeNoTraffic@0$kBluetoothL2CAPSegmentationAndReassemblyContinuationOfSDU@3$kBluetoothL2CAPSegmentationAndReassemblyEndOfSDU@2$kBluetoothL2CAPSegmentationAndReassemblyStartOfSDU@1$kBluetoothL2CAPSegmentationAndReassemblyUnsegmentedSDU@0$kBluetoothL2CAPSupervisoryFuctionTypeReceiverNotReady@2$kBluetoothL2CAPSupervisoryFuctionTypeReceiverReady@0$kBluetoothL2CAPSupervisoryFuctionTypeReject@1$kBluetoothL2CAPSupervisoryFuctionTypeSelectiveReject@3$kBluetoothL2CAPTCICommandL2CA_ConfigReq@3$kBluetoothL2CAPTCICommandL2CA_ConfigResp@19$kBluetoothL2CAPTCICommandL2CA_ConnectReq@1$kBluetoothL2CAPTCICommandL2CA_ConnectResp@17$kBluetoothL2CAPTCICommandL2CA_DisableCLT@4$kBluetoothL2CAPTCICommandL2CA_DisconnectReq@2$kBluetoothL2CAPTCICommandL2CA_DisconnectResp@18$kBluetoothL2CAPTCICommandL2CA_EnableCLT@5$kBluetoothL2CAPTCICommandL2CA_GetInfo@14$kBluetoothL2CAPTCICommandL2CA_GroupAddMember@8$kBluetoothL2CAPTCICommandL2CA_GroupClose@7$kBluetoothL2CAPTCICommandL2CA_GroupCreate@6$kBluetoothL2CAPTCICommandL2CA_GroupMembership@10$kBluetoothL2CAPTCICommandL2CA_GroupRemoveMember@9$kBluetoothL2CAPTCICommandL2CA_Ping@13$kBluetoothL2CAPTCICommandL2CA_ReadData@12$kBluetoothL2CAPTCICommandL2CA_Reserved1@15$kBluetoothL2CAPTCICommandL2CA_Reserved2@16$kBluetoothL2CAPTCICommandL2CA_WriteData@11$kBluetoothL2CAPTCICommandReserved@0$kBluetoothL2CAPTCIEventIDL2CA_ConfigInd@2$kBluetoothL2CAPTCIEventIDL2CA_ConnectInd@1$kBluetoothL2CAPTCIEventIDL2CA_DisconnectInd@3$kBluetoothL2CAPTCIEventIDL2CA_QoSViolationInd@4$kBluetoothL2CAPTCIEventIDL2CA_TimeOutInd@5$kBluetoothL2CAPTCIEventIDReserved@0$kBluetoothL2CAPUnicastConnectionlessDataReception@512$kBluetoothLEFeatureConnectionParamsRequestProcedure@2$kBluetoothLEFeatureExtendedRejectIndication@4$kBluetoothLEFeatureExtendedScannerFilterPolicies@128$kBluetoothLEFeatureLEDataPacketLengthExtension@32$kBluetoothLEFeatureLEEncryption@1$kBluetoothLEFeatureLEPing@16$kBluetoothLEFeatureLLPrivacy@64$kBluetoothLEFeaturePeripheralInitiatedFeaturesExchange@8$kBluetoothLEFeatureSlaveInitiatedFeaturesExchange@8$kBluetoothLEMaxTXOctetsDefault@128$kBluetoothLEMaxTXOctetsMax@251$kBluetoothLEMaxTXOctetsMin@27$kBluetoothLEMaxTXTimeDefault@27$kBluetoothLEMaxTXTimeMax@2120$kBluetoothLEMaxTXTimeMin@328$kBluetoothLESMPMaxEncryptionKeySize@16$kBluetoothLESMPMinEncryptionKeySize@7$kBluetoothLESMPTimeout@30$kBluetoothLESecurityManagerBonding@1$kBluetoothLESecurityManagerCommandCodeEncryptionInfo@6$kBluetoothLESecurityManagerCommandCodeIdentityAddressInfo@9$kBluetoothLESecurityManagerCommandCodeIdentityInfo@8$kBluetoothLESecurityManagerCommandCodeMasterIdentification@7$kBluetoothLESecurityManagerCommandCodePairingConfirm@3$kBluetoothLESecurityManagerCommandCodePairingDHKeyCheck@13$kBluetoothLESecurityManagerCommandCodePairingFailed@5$kBluetoothLESecurityManagerCommandCodePairingKeypressNotification@14$kBluetoothLESecurityManagerCommandCodePairingPublicKey@12$kBluetoothLESecurityManagerCommandCodePairingRandom@4$kBluetoothLESecurityManagerCommandCodePairingRequest@1$kBluetoothLESecurityManagerCommandCodePairingResponse@2$kBluetoothLESecurityManagerCommandCodeReserved@0$kBluetoothLESecurityManagerCommandCodeReservedEnd@255$kBluetoothLESecurityManagerCommandCodeReservedStart@15$kBluetoothLESecurityManagerCommandCodeSecurityRequest@11$kBluetoothLESecurityManagerCommandCodeSigningInfo@10$kBluetoothLESecurityManagerEncryptionKey@1$kBluetoothLESecurityManagerIDKey@2$kBluetoothLESecurityManagerIOCapabilityDisplayOnly@0$kBluetoothLESecurityManagerIOCapabilityDisplayYesNo@1$kBluetoothLESecurityManagerIOCapabilityKeyboardDisplay@4$kBluetoothLESecurityManagerIOCapabilityKeyboardOnly@2$kBluetoothLESecurityManagerIOCapabilityNoInputNoOutput@3$kBluetoothLESecurityManagerIOCapabilityReservedEnd@255$kBluetoothLESecurityManagerIOCapabilityReservedStart@5$kBluetoothLESecurityManagerLinkKey@8$kBluetoothLESecurityManagerNoBonding@0$kBluetoothLESecurityManagerNotificationTypePasskeyCleared@3$kBluetoothLESecurityManagerNotificationTypePasskeyDigitEntered@1$kBluetoothLESecurityManagerNotificationTypePasskeyDigitErased@2$kBluetoothLESecurityManagerNotificationTypePasskeyEntryCompleted@4$kBluetoothLESecurityManagerNotificationTypePasskeyEntryStarted@0$kBluetoothLESecurityManagerNotificationTypeReservedEnd@255$kBluetoothLESecurityManagerNotificationTypeReservedStart@5$kBluetoothLESecurityManagerOOBAuthenticationDataNotPresent@0$kBluetoothLESecurityManagerOOBAuthenticationDataPresent@1$kBluetoothLESecurityManagerOOBDataReservedEnd@255$kBluetoothLESecurityManagerOOBDataReservedStart@2$kBluetoothLESecurityManagerReasonCodeAuthenticationRequirements@3$kBluetoothLESecurityManagerReasonCodeBREDRPairingInProgress@13$kBluetoothLESecurityManagerReasonCodeCommandNotSupported@7$kBluetoothLESecurityManagerReasonCodeConfirmValueFailed@4$kBluetoothLESecurityManagerReasonCodeCrossTransportKeyDerivationGenerationNotAllowed@14$kBluetoothLESecurityManagerReasonCodeDHKeyCheckFailed@11$kBluetoothLESecurityManagerReasonCodeEncryptionKeySize@6$kBluetoothLESecurityManagerReasonCodeInvalidParameters@10$kBluetoothLESecurityManagerReasonCodeNumericComparisonFailed@12$kBluetoothLESecurityManagerReasonCodeOOBNotAvailbale@2$kBluetoothLESecurityManagerReasonCodePairingNotSupported@5$kBluetoothLESecurityManagerReasonCodePasskeyEntryFailed@1$kBluetoothLESecurityManagerReasonCodeRepeatedAttempts@9$kBluetoothLESecurityManagerReasonCodeReserved@0$kBluetoothLESecurityManagerReasonCodeReservedEnd@255$kBluetoothLESecurityManagerReasonCodeReservedStart@15$kBluetoothLESecurityManagerReasonCodeUnspecifiedReason@8$kBluetoothLESecurityManagerReservedEnd@3$kBluetoothLESecurityManagerReservedStart@2$kBluetoothLESecurityManagerSignKey@4$kBluetoothLESecurityManagerUserInputCapabilityKeyboard@3$kBluetoothLESecurityManagerUserInputCapabilityNoInput@1$kBluetoothLESecurityManagerUserInputCapabilityYesNo@2$kBluetoothLESecurityManagerUserOutputCapabilityNoOutput@1$kBluetoothLESecurityManagerUserOutputCapabilityNumericOutput@2$kBluetoothLETXOctetsDefault@27$kBluetoothLETXOctetsMax@251$kBluetoothLETXOctetsMin@27$kBluetoothLETXTimeDefault@328$kBluetoothLETXTimeMax@2120$kBluetoothLETXTimeMin@328$kBluetoothLMPVersionCoreSpecification1_0b@0$kBluetoothLMPVersionCoreSpecification1_1@1$kBluetoothLMPVersionCoreSpecification1_2@2$kBluetoothLMPVersionCoreSpecification2_0EDR@3$kBluetoothLMPVersionCoreSpecification2_1EDR@4$kBluetoothLMPVersionCoreSpecification3_0HS@5$kBluetoothLMPVersionCoreSpecification4_0@6$kBluetoothLMPVersionCoreSpecification4_1@7$kBluetoothLMPVersionCoreSpecification4_2@8$kBluetoothLMPVersionCoreSpecification5_0@9$kBluetoothLMPVersionCoreSpecification5_1@10$kBluetoothLMPVersionCoreSpecification5_2@11$kBluetoothLimitedInquiryAccessCodeIndex@1$kBluetoothLimitedInquiryAccessCodeLAPValue@10390272$kBluetoothLinkTypeNone@255$kBluetoothOOBAuthenticationDataFromRemoteDevicePresent@1$kBluetoothOOBAuthenticationDataNotPresent@0$kBluetoothPacketType2DH1Omit@2$kBluetoothPacketType2DH3Omit@256$kBluetoothPacketType2DH5Omit@4096$kBluetoothPacketType3DH1Omit@4$kBluetoothPacketType3DH3Omit@512$kBluetoothPacketType3DM5Omit@8192$kBluetoothPacketTypeAUX@512$kBluetoothPacketTypeDH1@16$kBluetoothPacketTypeDH3@2048$kBluetoothPacketTypeDH5@32768$kBluetoothPacketTypeDM1@8$kBluetoothPacketTypeDM3@1024$kBluetoothPacketTypeDM5@16384$kBluetoothPacketTypeDV@256$kBluetoothPacketTypeHV1@32$kBluetoothPacketTypeHV2@64$kBluetoothPacketTypeHV3@128$kBluetoothPacketTypeReserved1@1$kBluetoothPageScanModeMandatory@0$kBluetoothPageScanModeOptional1@1$kBluetoothPageScanModeOptional2@2$kBluetoothPageScanModeOptional3@3$kBluetoothPageScanPeriodModeP0@0$kBluetoothPageScanPeriodModeP1@1$kBluetoothPageScanPeriodModeP2@2$kBluetoothPageScanRepetitionModeR0@0$kBluetoothPageScanRepetitionModeR1@1$kBluetoothPageScanRepetitionModeR2@2$kBluetoothRFCOMMParityTypeEvenParity@2$kBluetoothRFCOMMParityTypeMaxParity@3$kBluetoothRFCOMMParityTypeNoParity@0$kBluetoothRFCOMMParityTypeOddParity@1$kBluetoothRoleBecomeCentral@0$kBluetoothRoleBecomeMaster@0$kBluetoothRoleRemainPeripheral@1$kBluetoothRoleRemainSlave@1$kBluetoothSCOConnection@0$kBluetoothSDPAttributeDeviceIdentifierClientExecutableURL@11$kBluetoothSDPAttributeDeviceIdentifierDocumentationURL@10$kBluetoothSDPAttributeDeviceIdentifierPrimaryRecord@516$kBluetoothSDPAttributeDeviceIdentifierProductID@514$kBluetoothSDPAttributeDeviceIdentifierReservedRangeEnd@767$kBluetoothSDPAttributeDeviceIdentifierReservedRangeStart@518$kBluetoothSDPAttributeDeviceIdentifierServiceDescription@1$kBluetoothSDPAttributeDeviceIdentifierSpecificationID@512$kBluetoothSDPAttributeDeviceIdentifierVendorID@513$kBluetoothSDPAttributeDeviceIdentifierVendorIDSource@517$kBluetoothSDPAttributeDeviceIdentifierVersion@515$kBluetoothSDPAttributeIdentifierAdditionalProtocolsDescriptorList@13$kBluetoothSDPAttributeIdentifierAudioFeedbackSupport@773$kBluetoothSDPAttributeIdentifierBluetoothProfileDescriptorList@9$kBluetoothSDPAttributeIdentifierBrowseGroupList@5$kBluetoothSDPAttributeIdentifierClientExecutableURL@11$kBluetoothSDPAttributeIdentifierDocumentationURL@10$kBluetoothSDPAttributeIdentifierExternalNetwork@769$kBluetoothSDPAttributeIdentifierFaxClass1Support@770$kBluetoothSDPAttributeIdentifierFaxClass2Support@772$kBluetoothSDPAttributeIdentifierFaxClass2_0Support@771$kBluetoothSDPAttributeIdentifierGroupID@512$kBluetoothSDPAttributeIdentifierHIDBatteryPower@521$kBluetoothSDPAttributeIdentifierHIDBootDevice@526$kBluetoothSDPAttributeIdentifierHIDCountryCode@515$kBluetoothSDPAttributeIdentifierHIDDescriptorList@518$kBluetoothSDPAttributeIdentifierHIDDeviceSubclass@514$kBluetoothSDPAttributeIdentifierHIDLangIDBaseList@519$kBluetoothSDPAttributeIdentifierHIDNormallyConnectable@525$kBluetoothSDPAttributeIdentifierHIDParserVersion@513$kBluetoothSDPAttributeIdentifierHIDProfileVersion@523$kBluetoothSDPAttributeIdentifierHIDReconnectInitiate@517$kBluetoothSDPAttributeIdentifierHIDReleaseNumber@512$kBluetoothSDPAttributeIdentifierHIDRemoteWake@522$kBluetoothSDPAttributeIdentifierHIDSDPDisable@520$kBluetoothSDPAttributeIdentifierHIDSSRHostMaxLatency@527$kBluetoothSDPAttributeIdentifierHIDSSRHostMinTimeout@528$kBluetoothSDPAttributeIdentifierHIDSupervisionTimeout@524$kBluetoothSDPAttributeIdentifierHIDVirtualCable@516$kBluetoothSDPAttributeIdentifierHomepageURL@776$kBluetoothSDPAttributeIdentifierIPSubnet@512$kBluetoothSDPAttributeIdentifierIconURL@12$kBluetoothSDPAttributeIdentifierLanguageBaseAttributeIDList@6$kBluetoothSDPAttributeIdentifierMaxNetAccessRate@780$kBluetoothSDPAttributeIdentifierNetAccessType@779$kBluetoothSDPAttributeIdentifierNetwork@769$kBluetoothSDPAttributeIdentifierNetworkAddress@774$kBluetoothSDPAttributeIdentifierProtocolDescriptorList@4$kBluetoothSDPAttributeIdentifierProviderName@2$kBluetoothSDPAttributeIdentifierRemoteAudioVolumeControl@770$kBluetoothSDPAttributeIdentifierSecurityDescription@778$kBluetoothSDPAttributeIdentifierServiceAvailability@8$kBluetoothSDPAttributeIdentifierServiceClassIDList@1$kBluetoothSDPAttributeIdentifierServiceDatabaseState@513$kBluetoothSDPAttributeIdentifierServiceDescription@1$kBluetoothSDPAttributeIdentifierServiceID@3$kBluetoothSDPAttributeIdentifierServiceInfoTimeToLive@7$kBluetoothSDPAttributeIdentifierServiceName@0$kBluetoothSDPAttributeIdentifierServiceRecordHandle@0$kBluetoothSDPAttributeIdentifierServiceRecordState@2$kBluetoothSDPAttributeIdentifierServiceVersion@768$kBluetoothSDPAttributeIdentifierSupportedCapabilities@784$kBluetoothSDPAttributeIdentifierSupportedDataStoresList@769$kBluetoothSDPAttributeIdentifierSupportedFeatures@785$kBluetoothSDPAttributeIdentifierSupportedFunctions@786$kBluetoothSDPAttributeIdentifierSupporterFormatsList@771$kBluetoothSDPAttributeIdentifierTotalImagingDataCapacity@787$kBluetoothSDPAttributeIdentifierVersionNumberList@512$kBluetoothSDPAttributeIdentifierWAPGateway@775$kBluetoothSDPAttributeIdentifierWAPStackType@777$kBluetoothSDPDataElementTypeBoolean@5$kBluetoothSDPDataElementTypeDataElementAlternative@7$kBluetoothSDPDataElementTypeDataElementSequence@6$kBluetoothSDPDataElementTypeNil@0$kBluetoothSDPDataElementTypeReservedEnd@31$kBluetoothSDPDataElementTypeReservedStart@9$kBluetoothSDPDataElementTypeSignedInt@2$kBluetoothSDPDataElementTypeString@4$kBluetoothSDPDataElementTypeURL@8$kBluetoothSDPDataElementTypeUUID@3$kBluetoothSDPDataElementTypeUnsignedInt@1$kBluetoothSDPErrorCodeInsufficientResources@6$kBluetoothSDPErrorCodeInvalidContinuationState@5$kBluetoothSDPErrorCodeInvalidPDUSize@4$kBluetoothSDPErrorCodeInvalidRequestSyntax@3$kBluetoothSDPErrorCodeInvalidSDPVersion@1$kBluetoothSDPErrorCodeInvalidServiceRecordHandle@2$kBluetoothSDPErrorCodeReserved@0$kBluetoothSDPErrorCodeReservedEnd@65535$kBluetoothSDPErrorCodeReservedStart@7$kBluetoothSDPErrorCodeSuccess@0$kBluetoothSDPPDUIDErrorResponse@1$kBluetoothSDPPDUIDReserved@0$kBluetoothSDPPDUIDServiceAttributeRequest@4$kBluetoothSDPPDUIDServiceAttributeResponse@5$kBluetoothSDPPDUIDServiceSearchAttributeRequest@6$kBluetoothSDPPDUIDServiceSearchAttributeResponse@7$kBluetoothSDPPDUIDServiceSearchRequest@2$kBluetoothSDPPDUIDServiceSearchResponse@3$kBluetoothSDPProtocolParameterBNEPSupportedNetworkPacketTypeList@2$kBluetoothSDPProtocolParameterBNEPVersion@1$kBluetoothSDPProtocolParameterL2CAPPSM@1$kBluetoothSDPProtocolParameterRFCOMMChannel@1$kBluetoothSDPProtocolParameterTCPPort@1$kBluetoothSDPProtocolParameterUDPPort@1$kBluetoothSDPUUID16ATT@7$kBluetoothSDPUUID16AVCTP@23$kBluetoothSDPUUID16AVDTP@25$kBluetoothSDPUUID16BNEP@15$kBluetoothSDPUUID16Base@0$kBluetoothSDPUUID16CMPT@27$kBluetoothSDPUUID16FTP@10$kBluetoothSDPUUID16HIDP@17$kBluetoothSDPUUID16HTTP@12$kBluetoothSDPUUID16HardcopyControlChannel@18$kBluetoothSDPUUID16HardcopyDataChannel@20$kBluetoothSDPUUID16HardcopyNotification@22$kBluetoothSDPUUID16IP@9$kBluetoothSDPUUID16L2CAP@256$kBluetoothSDPUUID16MCAPControlChannel@30$kBluetoothSDPUUID16MCAPDataChannel@31$kBluetoothSDPUUID16OBEX@8$kBluetoothSDPUUID16RFCOMM@3$kBluetoothSDPUUID16SDP@1$kBluetoothSDPUUID16ServiceClassAVRemoteControl@4366$kBluetoothSDPUUID16ServiceClassAVRemoteControlController@4367$kBluetoothSDPUUID16ServiceClassAVRemoteControlTarget@4364$kBluetoothSDPUUID16ServiceClassAdvancedAudioDistribution@4365$kBluetoothSDPUUID16ServiceClassAudioSink@4363$kBluetoothSDPUUID16ServiceClassAudioSource@4362$kBluetoothSDPUUID16ServiceClassAudioVideo@4396$kBluetoothSDPUUID16ServiceClassBasicPrinting@4386$kBluetoothSDPUUID16ServiceClassBrowseGroupDescriptor@4097$kBluetoothSDPUUID16ServiceClassCommonISDNAccess@4392$kBluetoothSDPUUID16ServiceClassCordlessTelephony@4361$kBluetoothSDPUUID16ServiceClassDialupNetworking@4355$kBluetoothSDPUUID16ServiceClassDirectPrinting@4376$kBluetoothSDPUUID16ServiceClassDirectPrintingReferenceObjectsService@4384$kBluetoothSDPUUID16ServiceClassFax@4369$kBluetoothSDPUUID16ServiceClassGATT@6145$kBluetoothSDPUUID16ServiceClassGN@4375$kBluetoothSDPUUID16ServiceClassGenericAudio@4611$kBluetoothSDPUUID16ServiceClassGenericFileTransfer@4610$kBluetoothSDPUUID16ServiceClassGenericNetworking@4609$kBluetoothSDPUUID16ServiceClassGenericTelephony@4612$kBluetoothSDPUUID16ServiceClassGlobalNavigationSatelliteSystem@4405$kBluetoothSDPUUID16ServiceClassGlobalNavigationSatelliteSystemServer@4406$kBluetoothSDPUUID16ServiceClassHCR_Print@4390$kBluetoothSDPUUID16ServiceClassHCR_Scan@4391$kBluetoothSDPUUID16ServiceClassHandsFree@4382$kBluetoothSDPUUID16ServiceClassHandsFreeAudioGateway@4383$kBluetoothSDPUUID16ServiceClassHardcopyCableReplacement@4389$kBluetoothSDPUUID16ServiceClassHeadset@4360$kBluetoothSDPUUID16ServiceClassHeadsetAudioGateway@4370$kBluetoothSDPUUID16ServiceClassHeadset_HS@4401$kBluetoothSDPUUID16ServiceClassHealthDevice@5120$kBluetoothSDPUUID16ServiceClassHealthDeviceSink@5122$kBluetoothSDPUUID16ServiceClassHealthDeviceSource@5121$kBluetoothSDPUUID16ServiceClassHumanInterfaceDeviceService@4388$kBluetoothSDPUUID16ServiceClassImaging@4378$kBluetoothSDPUUID16ServiceClassImagingAutomaticArchive@4380$kBluetoothSDPUUID16ServiceClassImagingReferencedObjects@4381$kBluetoothSDPUUID16ServiceClassImagingResponder@4379$kBluetoothSDPUUID16ServiceClassIntercom@4368$kBluetoothSDPUUID16ServiceClassIrMCSync@4356$kBluetoothSDPUUID16ServiceClassIrMCSyncCommand@4359$kBluetoothSDPUUID16ServiceClassLANAccessUsingPPP@4354$kBluetoothSDPUUID16ServiceClassMessageAccessProfile@4404$kBluetoothSDPUUID16ServiceClassMessageAccessServer@4402$kBluetoothSDPUUID16ServiceClassMessageNotificationServer@4403$kBluetoothSDPUUID16ServiceClassNAP@4374$kBluetoothSDPUUID16ServiceClassOBEXFileTransfer@4358$kBluetoothSDPUUID16ServiceClassOBEXObjectPush@4357$kBluetoothSDPUUID16ServiceClassPANU@4373$kBluetoothSDPUUID16ServiceClassPhonebookAccess@4400$kBluetoothSDPUUID16ServiceClassPhonebookAccess_PCE@4398$kBluetoothSDPUUID16ServiceClassPhonebookAccess_PSE@4399$kBluetoothSDPUUID16ServiceClassPnPInformation@4608$kBluetoothSDPUUID16ServiceClassPrintingStatus@4387$kBluetoothSDPUUID16ServiceClassPublicBrowseGroup@4098$kBluetoothSDPUUID16ServiceClassReferencePrinting@4377$kBluetoothSDPUUID16ServiceClassReflectedUI@4385$kBluetoothSDPUUID16ServiceClassSIM_Access@4397$kBluetoothSDPUUID16ServiceClassSerialPort@4353$kBluetoothSDPUUID16ServiceClassServiceDiscoveryServer@4096$kBluetoothSDPUUID16ServiceClassUDI_MT@4394$kBluetoothSDPUUID16ServiceClassUDI_TA@4395$kBluetoothSDPUUID16ServiceClassVideoConferencingGW@4393$kBluetoothSDPUUID16ServiceClassVideoDistribution@4869$kBluetoothSDPUUID16ServiceClassVideoSink@4868$kBluetoothSDPUUID16ServiceClassVideoSource@4867$kBluetoothSDPUUID16ServiceClassWAP@4371$kBluetoothSDPUUID16ServiceClassWAPClient@4372$kBluetoothSDPUUID16TCP@4$kBluetoothSDPUUID16TCSAT@6$kBluetoothSDPUUID16TCSBIN@5$kBluetoothSDPUUID16UDI_C_Plane@29$kBluetoothSDPUUID16UDP@2$kBluetoothSDPUUID16UPNP@16$kBluetoothSDPUUID16WSP@14$kBluetoothServiceClassMajorAny@*********$kBluetoothServiceClassMajorAudio@256$kBluetoothServiceClassMajorCapturing@64$kBluetoothServiceClassMajorInformation@1024$kBluetoothServiceClassMajorLimitedDiscoverableMode@1$kBluetoothServiceClassMajorNetworking@16$kBluetoothServiceClassMajorNone@**********$kBluetoothServiceClassMajorObjectTransfer@128$kBluetoothServiceClassMajorPositioning@8$kBluetoothServiceClassMajorRendering@32$kBluetoothServiceClassMajorReserved1@2$kBluetoothServiceClassMajorReserved2@4$kBluetoothServiceClassMajorTelephony@512$kBluetoothSynchronousConnectionPacketType2EV3Omit@64$kBluetoothSynchronousConnectionPacketType2EV5Omit@256$kBluetoothSynchronousConnectionPacketType3EV3Omit@128$kBluetoothSynchronousConnectionPacketType3EV5Omit@512$kBluetoothSynchronousConnectionPacketTypeAll@65535$kBluetoothSynchronousConnectionPacketTypeEV3@8$kBluetoothSynchronousConnectionPacketTypeEV4@16$kBluetoothSynchronousConnectionPacketTypeEV5@32$kBluetoothSynchronousConnectionPacketTypeFutureUse@64512$kBluetoothSynchronousConnectionPacketTypeHV1@1$kBluetoothSynchronousConnectionPacketTypeHV2@2$kBluetoothSynchronousConnectionPacketTypeHV3@4$kBluetoothSynchronousConnectionPacketTypeNone@0$kBluetoothTransportTypePCCard@2$kBluetoothTransportTypePCICard@3$kBluetoothTransportTypePCIe@5$kBluetoothTransportTypeUART@4$kBluetoothTransportTypeUSB@1$kBluetoothVoiceSettingAirCodingFormatALaw@2$kBluetoothVoiceSettingAirCodingFormatCVSD@0$kBluetoothVoiceSettingAirCodingFormatMask@3$kBluetoothVoiceSettingAirCodingFormatTransparentData@3$kBluetoothVoiceSettingAirCodingFormatULaw@1$kBluetoothVoiceSettingInputCodingALawInputCoding@512$kBluetoothVoiceSettingInputCodingLinearInputCoding@0$kBluetoothVoiceSettingInputCodingMask@768$kBluetoothVoiceSettingInputCodingULawInputCoding@256$kBluetoothVoiceSettingInputDataFormat1sComplement@0$kBluetoothVoiceSettingInputDataFormat2sComplement@64$kBluetoothVoiceSettingInputDataFormatMask@192$kBluetoothVoiceSettingInputDataFormatSignMagnitude@128$kBluetoothVoiceSettingInputDataFormatUnsigned@192$kBluetoothVoiceSettingInputSampleSize16Bit@32$kBluetoothVoiceSettingInputSampleSize8Bit@0$kBluetoothVoiceSettingInputSampleSizeMask@32$kBluetoothVoiceSettingPCMBitPositionMask@28$kConnectionActiveMode@0$kConnectionHoldMode@1$kConnectionModeReservedForFutureUse@4$kConnectionParkMode@3$kConnectionSniffMode@2$kDefaultPageTimeout@10000$kDeleteAllStoredLinkKeys@1$kDeleteKeyForSpecifiedDeviceOnly@0$kDisableAllLMModes@0$kEnableCentralPeripheralSwitch@1$kEnableHoldMode@2$kEnableMasterSlaveSwitch@1$kEnableParkMode@8$kEnableSniffMode@4$kEncryptionDisabled@0$kEncryptionForBothPointToPointAndBroadcastPackets@2$kEncryptionOnlyForPointToPointPackets@1$kFTSFileTypeFile@2$kFTSFileTypeFolder@1$kHCIACLDataPacketsOffHCISCODataPacketsOn@2$kHCIACLDataPacketsOnHCISCODataPacketsOff@1$kHCIACLDataPacketsOnHCISCODataPacketsOn@3$kHCIRetransmissionEffortTypeAtLeastOneAndOptimizeForPower@1$kHCIRetransmissionEffortTypeAtLeastOneAndOptimizeLinkQuality@2$kHCIRetransmissionEffortTypeDontCare@255$kHCIRetransmissionEffortTypeNone@0$kHostControllerToHostFlowControlOff@0$kIOBluetoothDeviceSearchClassic@1$kIOBluetoothDeviceSearchLE@2$kIOBluetoothL2CAPChannelEventTypeClosed@3$kIOBluetoothL2CAPChannelEventTypeData@1$kIOBluetoothL2CAPChannelEventTypeOpenComplete@2$kIOBluetoothL2CAPChannelEventTypeQueueSpaceAvailable@6$kIOBluetoothL2CAPChannelEventTypeReconfigured@4$kIOBluetoothL2CAPChannelEventTypeWriteComplete@5$kIOBluetoothObjectIDNULL@0$kIOBluetoothUserNotificationChannelDirectionAny@0$kIOBluetoothUserNotificationChannelDirectionIncoming@1$kIOBluetoothUserNotificationChannelDirectionOutgoing@2$kInfoStringMaxLength@35$kInquiryScanDisabledPageScanEnabled@2$kInquiryScanEnabledPageScanDisabled@1$kInquiryScanEnabledPageScanEnabled@3$kMaintainCurrentPowerState@0$kMandatoryPageScanMode@0$kMaxChannelIDPerSide@31$kMaximumNumberOfInquiryAccessCodes@64$kNoScansEnabled@0$kOBEXBadArgumentError@-21854$kOBEXBadRequestError@-21856$kOBEXCancelledError@-21857$kOBEXConflictError@-21861$kOBEXConnectFlag1Reserved@2$kOBEXConnectFlag2Reserved@4$kOBEXConnectFlag3Reserved@8$kOBEXConnectFlag4Reserved@16$kOBEXConnectFlag5Reserved@32$kOBEXConnectFlag6Reserved@64$kOBEXConnectFlag7Reserved@128$kOBEXConnectFlagNone@0$kOBEXConnectFlagSupportMultipleItLMPConnections@1$kOBEXErrorRangeMax@-21899$kOBEXErrorRangeMin@-21850$kOBEXForbiddenError@-21858$kOBEXGeneralError@-21850$kOBEXHeaderIDAppParameters@76$kOBEXHeaderIDAuthorizationChallenge@77$kOBEXHeaderIDAuthorizationResponse@78$kOBEXHeaderIDBody@72$kOBEXHeaderIDConnectionID@203$kOBEXHeaderIDCount@192$kOBEXHeaderIDDescription@5$kOBEXHeaderIDEndOfBody@73$kOBEXHeaderIDHTTP@71$kOBEXHeaderIDLength@195$kOBEXHeaderIDName@1$kOBEXHeaderIDOBEX13CreatorID@207$kOBEXHeaderIDOBEX13ObjectClass@81$kOBEXHeaderIDOBEX13SessionParameters@82$kOBEXHeaderIDOBEX13SessionSequenceNumber@147$kOBEXHeaderIDOBEX13WANUUID@80$kOBEXHeaderIDObjectClass@79$kOBEXHeaderIDReservedRangeEnd@47$kOBEXHeaderIDReservedRangeStart@16$kOBEXHeaderIDTarget@70$kOBEXHeaderIDTime4Byte@196$kOBEXHeaderIDTimeISO@68$kOBEXHeaderIDType@66$kOBEXHeaderIDUserDefinedRangeEnd@63$kOBEXHeaderIDUserDefinedRangeStart@48$kOBEXHeaderIDWho@74$kOBEXInternalError@-21853$kOBEXMethodNotAllowedError@-21862$kOBEXNoResourcesError@-21851$kOBEXNonceFlag2Reserved@4$kOBEXNonceFlag3Reserved@8$kOBEXNonceFlag4Reserved@16$kOBEXNonceFlag5Reserved@32$kOBEXNonceFlag6Reserved@64$kOBEXNonceFlag7Reserved@128$kOBEXNonceFlagAccessModeReadOnly@2$kOBEXNonceFlagNone@0$kOBEXNonceFlagSendUserIDInResponse@1$kOBEXNotAcceptableError@-21860$kOBEXNotFoundError@-21863$kOBEXNotImplementedError@-21864$kOBEXOpCodeAbort@255$kOBEXOpCodeCloseSession@1$kOBEXOpCodeConnect@128$kOBEXOpCodeCreateSession@0$kOBEXOpCodeDisconnect@129$kOBEXOpCodeGet@3$kOBEXOpCodeGetWithHighBitSet@131$kOBEXOpCodePut@2$kOBEXOpCodePutWithHighBitSet@130$kOBEXOpCodeReserved@4$kOBEXOpCodeReservedRangeEnd@15$kOBEXOpCodeReservedRangeStart@6$kOBEXOpCodeReservedWithHighBitSet@132$kOBEXOpCodeResumeSession@3$kOBEXOpCodeSetPath@133$kOBEXOpCodeSetTimeout@4$kOBEXOpCodeSuspendSession@2$kOBEXOpCodeUserDefinedEnd@31$kOBEXOpCodeUserDefinedStart@16$kOBEXPreconditionFailedError@-21865$kOBEXPutFlag2Reserved@4$kOBEXPutFlag3Reserved@8$kOBEXPutFlag4Reserved@16$kOBEXPutFlag5Reserved@32$kOBEXPutFlag6Reserved@64$kOBEXPutFlag7Reserved@128$kOBEXPutFlagDontCreateDirectory@2$kOBEXPutFlagGoToParentDirFirst@1$kOBEXPutFlagNone@0$kOBEXRealmASCII@0$kOBEXRealmISO88591@1$kOBEXRealmISO88592@2$kOBEXRealmISO88593@3$kOBEXRealmISO88594@4$kOBEXRealmISO88595@5$kOBEXRealmISO88596@6$kOBEXRealmISO88597@7$kOBEXRealmISO88598@8$kOBEXRealmISO88599@9$kOBEXRealmUNICODE@255$kOBEXResponseCodeAccepted@34$kOBEXResponseCodeAcceptedWithFinalBit@162$kOBEXResponseCodeBadGateway@82$kOBEXResponseCodeBadGatewayWithFinalBit@210$kOBEXResponseCodeBadRequest@64$kOBEXResponseCodeBadRequestWithFinalBit@192$kOBEXResponseCodeConflict@73$kOBEXResponseCodeConflictWithFinalBit@201$kOBEXResponseCodeContinue@16$kOBEXResponseCodeContinueWithFinalBit@144$kOBEXResponseCodeCreated@33$kOBEXResponseCodeCreatedWithFinalBit@161$kOBEXResponseCodeDatabaseFull@96$kOBEXResponseCodeDatabaseFullWithFinalBit@224$kOBEXResponseCodeDatabaseLocked@97$kOBEXResponseCodeDatabaseLockedWithFinalBit@225$kOBEXResponseCodeForbidden@67$kOBEXResponseCodeForbiddenWithFinalBit@195$kOBEXResponseCodeGatewayTimeout@84$kOBEXResponseCodeGatewayTimeoutWithFinalBit@212$kOBEXResponseCodeGone@74$kOBEXResponseCodeGoneWithFinalBit@202$kOBEXResponseCodeHTTPVersionNotSupported@85$kOBEXResponseCodeHTTPVersionNotSupportedWithFinalBit@213$kOBEXResponseCodeInternalServerError@80$kOBEXResponseCodeInternalServerErrorWithFinalBit@208$kOBEXResponseCodeLengthRequired@75$kOBEXResponseCodeLengthRequiredFinalBit@203$kOBEXResponseCodeMethodNotAllowed@69$kOBEXResponseCodeMethodNotAllowedWithFinalBit@197$kOBEXResponseCodeMovedPermanently@49$kOBEXResponseCodeMovedPermanentlyWithFinalBit@177$kOBEXResponseCodeMovedTemporarily@50$kOBEXResponseCodeMovedTemporarilyWithFinalBit@178$kOBEXResponseCodeMultipleChoices@48$kOBEXResponseCodeMultipleChoicesWithFinalBit@176$kOBEXResponseCodeNoContent@36$kOBEXResponseCodeNoContentWithFinalBit@164$kOBEXResponseCodeNonAuthoritativeInfo@35$kOBEXResponseCodeNonAuthoritativeInfoWithFinalBit@163$kOBEXResponseCodeNotAcceptable@70$kOBEXResponseCodeNotAcceptableWithFinalBit@198$kOBEXResponseCodeNotFound@68$kOBEXResponseCodeNotFoundWithFinalBit@196$kOBEXResponseCodeNotImplemented@81$kOBEXResponseCodeNotImplementedWithFinalBit@209$kOBEXResponseCodeNotModified@52$kOBEXResponseCodeNotModifiedWithFinalBit@180$kOBEXResponseCodePartialContent@38$kOBEXResponseCodePartialContentWithFinalBit@166$kOBEXResponseCodePaymentRequired@66$kOBEXResponseCodePaymentRequiredWithFinalBit@194$kOBEXResponseCodePreconditionFailed@76$kOBEXResponseCodePreconditionFailedWithFinalBit@204$kOBEXResponseCodeProxyAuthenticationRequired@71$kOBEXResponseCodeProxyAuthenticationRequiredWithFinalBit@199$kOBEXResponseCodeRequestTimeOut@72$kOBEXResponseCodeRequestTimeOutWithFinalBit@200$kOBEXResponseCodeRequestURLTooLarge@78$kOBEXResponseCodeRequestURLTooLargeWithFinalBit@206$kOBEXResponseCodeRequestedEntityTooLarge@77$kOBEXResponseCodeRequestedEntityTooLargeWithFinalBit@205$kOBEXResponseCodeReservedRangeEnd@15$kOBEXResponseCodeReservedRangeStart@0$kOBEXResponseCodeResetContent@37$kOBEXResponseCodeResetContentWithFinalBit@165$kOBEXResponseCodeSeeOther@51$kOBEXResponseCodeSeeOtherWithFinalBit@179$kOBEXResponseCodeServiceUnavailable@83$kOBEXResponseCodeServiceUnavailableWithFinalBit@211$kOBEXResponseCodeSuccess@32$kOBEXResponseCodeSuccessWithFinalBit@160$kOBEXResponseCodeUnauthorized@65$kOBEXResponseCodeUnauthorizedWithFinalBit@193$kOBEXResponseCodeUnsupportedMediaType@79$kOBEXResponseCodeUnsupportedMediaTypeWithFinalBit@207$kOBEXResponseCodeUseProxy@53$kOBEXResponseCodeUseProxyWithFinalBit@181$kOBEXSessionAlreadyConnectedError@-21882$kOBEXSessionBadRequestError@-21877$kOBEXSessionBadResponseError@-21878$kOBEXSessionBusyError@-21875$kOBEXSessionEventTypeAbortCommandReceived@1330857281$kOBEXSessionEventTypeAbortCommandResponseReceived@1329808705$kOBEXSessionEventTypeConnectCommandReceived@1330857283$kOBEXSessionEventTypeConnectCommandResponseReceived@1329808707$kOBEXSessionEventTypeDisconnectCommandReceived@1330857284$kOBEXSessionEventTypeDisconnectCommandResponseReceived@1329808708$kOBEXSessionEventTypeError@1330070853$kOBEXSessionEventTypeGetCommandReceived@1330857287$kOBEXSessionEventTypeGetCommandResponseReceived@1329808711$kOBEXSessionEventTypePutCommandReceived@1330857296$kOBEXSessionEventTypePutCommandResponseReceived@1329808720$kOBEXSessionEventTypeSetPathCommandReceived@1330857299$kOBEXSessionEventTypeSetPathCommandResponseReceived@1329808723$kOBEXSessionNoTransportError@-21879$kOBEXSessionNotConnectedError@-21876$kOBEXSessionParameterTagDeviceAddress@0$kOBEXSessionParameterTagNextSequenceNumber@3$kOBEXSessionParameterTagNonce@1$kOBEXSessionParameterTagSessionID@2$kOBEXSessionParameterTagSessionOpcode@5$kOBEXSessionParameterTagTimeout@4$kOBEXSessionTimeoutError@-21881$kOBEXSessionTransportDiedError@-21880$kOBEXSuccess@0$kOBEXTimeoutError@-21855$kOBEXTransportEventTypeDataReceived@1147237441$kOBEXTransportEventTypeStatus@1400136020$kOBEXUnauthorizedError@-21859$kOBEXUnsupportedError@-21852$kOBEXVersion10@16$kOptionalPageScanMode1@1$kOptionalPageScanMode2@2$kOptionalPageScanMode3@3$kP0Mode@0$kP1Mode@1$kP2Mode@2$kReadAllStoredLinkKeys@1$kReadCurrentTransmitPowerLevel@0$kReadMaximumTransmitPowerLevel@1$kReservedForFutureUse@16$kReturnLinkKeyForSpecifiedDeviceOnly@0$kSCOFlowControlDisabled@0$kSCOFlowControlEnabled@1$kSearchOptionsAlwaysStartInquiry@1$kSearchOptionsDiscardCachedResults@2$kSearchOptionsNone@0$kSuspendInquiryScan@2$kSuspendPageScan@1$kSuspendPeriodicInquiries@3$"""
misc.update(
    {
        "BluetoothHCIGeneralFlowControlStates": NewType(
            "BluetoothHCIGeneralFlowControlStates", int
        ),
        "BluetoothAMPDisconnectPhysicalLinkResponseStatus": NewType(
            "BluetoothAMPDisconnectPhysicalLinkResponseStatus", int
        ),
        "IOBluetoothDeviceSearchOptionsBits": NewType(
            "IOBluetoothDeviceSearchOptionsBits", int
        ),
        "OBEXConnectFlagValues": NewType("OBEXConnectFlagValues", int),
        "IOBluetoothHandsFreeSMSSupport": NewType(
            "IOBluetoothHandsFreeSMSSupport", int
        ),
        "BluetoothHCIInquiryModes": NewType("BluetoothHCIInquiryModes", int),
        "OBEXNonceFlagValues": NewType("OBEXNonceFlagValues", int),
        "OBEXTransportEventType": NewType("OBEXTransportEventType", int),
        "BluetoothHCIFECRequiredValues": NewType("BluetoothHCIFECRequiredValues", int),
        "BluetoothTransportTypes": NewType("BluetoothTransportTypes", int),
        "OBEXRealmValues": NewType("OBEXRealmValues", int),
        "BluetoothHCICommandOpCodeGroup": NewType(
            "BluetoothHCICommandOpCodeGroup", int
        ),
        "BluetoothHCIAFHChannelAssessmentModes": NewType(
            "BluetoothHCIAFHChannelAssessmentModes", int
        ),
        "BluetoothHCIConnectionModes": NewType("BluetoothHCIConnectionModes", int),
        "BluetoothL2CAPSupervisoryFuctionType": NewType(
            "BluetoothL2CAPSupervisoryFuctionType", int
        ),
        "BluetoothLESecurityManagerKeyDistributionFormat": NewType(
            "BluetoothLESecurityManagerKeyDistributionFormat", int
        ),
        "BluetoothHCICommandOpCodeCommand": NewType(
            "BluetoothHCICommandOpCodeCommand", int
        ),
        "OBEXOpCodeSessionValues": NewType("OBEXOpCodeSessionValues", int),
        "BluetoothHCIEncryptionMode": NewType("BluetoothHCIEncryptionMode", int),
        "BluetoothAMPManagerCode": NewType("BluetoothAMPManagerCode", int),
        "BluetoothL2CAPInformationResult": NewType(
            "BluetoothL2CAPInformationResult", int
        ),
        "BluetoothRFCOMMParityType": NewType("BluetoothRFCOMMParityType", int),
        "BluetoothLEAdvertisingType": NewType("BluetoothLEAdvertisingType", int),
        "BluetoothHCIHoldModeActivityStates": NewType(
            "BluetoothHCIHoldModeActivityStates", int
        ),
        "BluetoothAMPGetAssocResponseStatus": NewType(
            "BluetoothAMPGetAssocResponseStatus", int
        ),
        "BluetoothAuthenticationRequirementsValues": NewType(
            "BluetoothAuthenticationRequirementsValues", int
        ),
        "BluetoothHCIEncryptionModes": NewType("BluetoothHCIEncryptionModes", int),
        "BluetoothHCITransmitReadPowerLevelTypes": NewType(
            "BluetoothHCITransmitReadPowerLevelTypes", int
        ),
        "BluetoothLMPVersions": NewType("BluetoothLMPVersions", int),
        "BluetoothLEConnectionInterval": NewType("BluetoothLEConnectionInterval", int),
        "BluetoothHCIAuthentionEnableModes": NewType(
            "BluetoothHCIAuthentionEnableModes", int
        ),
        "BluetoothL2CAPQoSType": NewType("BluetoothL2CAPQoSType", int),
        "BluetoothIOCapability": NewType("BluetoothIOCapability", int),
        "BluetoothLEAddressType": NewType("BluetoothLEAddressType", int),
        "BluetoothAMPCreatePhysicalLinkResponseStatus": NewType(
            "BluetoothAMPCreatePhysicalLinkResponseStatus", int
        ),
        "BluetoothLinkType": NewType("BluetoothLinkType", int),
        "BluetoothL2CAPConnectionStatus": NewType(
            "BluetoothL2CAPConnectionStatus", int
        ),
        "BluetoothRole": NewType("BluetoothRole", int),
        "BluetoothHCIPageScanModes": NewType("BluetoothHCIPageScanModes", int),
        "BluetoothHCIHoldModeActivity": NewType("BluetoothHCIHoldModeActivity", int),
        "BluetoothLESecurityManagerUserOutputCapability": NewType(
            "BluetoothLESecurityManagerUserOutputCapability", int
        ),
        "BluetoothL2CAPConfigurationOption": NewType(
            "BluetoothL2CAPConfigurationOption", int
        ),
        "BluetoothOOBDataPresenceValues": NewType(
            "BluetoothOOBDataPresenceValues", int
        ),
        "OBEXHeaderIdentifiers": NewType("OBEXHeaderIdentifiers", int),
        "BluetoothHCIExtendedInquiryResponseDataTypes": NewType(
            "BluetoothHCIExtendedInquiryResponseDataTypes", int
        ),
        "OBEXErrorCodes": NewType("OBEXErrorCodes", int),
        "BluetoothRFCOMMMTU": NewType("BluetoothRFCOMMMTU", int),
        "BluetoothLESecurityManagerIOCapability": NewType(
            "BluetoothLESecurityManagerIOCapability", int
        ),
        "BluetoothL2CAPInformationExtendedFeaturesMask": NewType(
            "BluetoothL2CAPInformationExtendedFeaturesMask", int
        ),
        "BluetoothHCIPageScanTypes": NewType("BluetoothHCIPageScanTypes", int),
        "BluetoothIOCapabilities": NewType("BluetoothIOCapabilities", int),
        "BluetoothHCIStatus": NewType("BluetoothHCIStatus", int),
        "BluetoothAirMode": NewType("BluetoothAirMode", int),
        "BluetoothHCISCOFlowControlStates": NewType(
            "BluetoothHCISCOFlowControlStates", int
        ),
        "IOBluetoothDeviceSearchTypesBits": NewType(
            "IOBluetoothDeviceSearchTypesBits", int
        ),
        "IOBluetoothHandsFreeCodecID": NewType("IOBluetoothHandsFreeCodecID", int),
        "OBEXOpCodeCommandValues": NewType("OBEXOpCodeCommandValues", int),
        "BluetoothHCIRoles": NewType("BluetoothHCIRoles", int),
        "BluetoothHCIPageScanEnableState": NewType(
            "BluetoothHCIPageScanEnableState", int
        ),
        "BluetoothSDPErrorCode": NewType("BluetoothSDPErrorCode", int),
        "BluetoothFeatureBits": NewType("BluetoothFeatureBits", int),
        "BluetoothHCIPowerState": NewType("BluetoothHCIPowerState", int),
        "BluetoothLEScanFilter": NewType("BluetoothLEScanFilter", int),
        "BluetoothPageScanPeriodMode": NewType("BluetoothPageScanPeriodMode", int),
        "IOBluetoothHandsFreeCallHoldModes": NewType(
            "IOBluetoothHandsFreeCallHoldModes", int
        ),
        "BluetoothLESecurityManagerKeypressNotificationType": NewType(
            "BluetoothLESecurityManagerKeypressNotificationType", int
        ),
        "BluetoothL2CAPConfigurationRetransmissionAndFlowControlFlags": NewType(
            "BluetoothL2CAPConfigurationRetransmissionAndFlowControlFlags", int
        ),
        "OBEXSessionParameterTags": NewType("OBEXSessionParameterTags", int),
        "IOBluetoothHandsFreeDeviceFeatures": NewType(
            "IOBluetoothHandsFreeDeviceFeatures", int
        ),
        "BluetoothKeypressNotificationType": NewType(
            "BluetoothKeypressNotificationType", int
        ),
        "BluetoothHCITransmitPowerLevelType": NewType(
            "BluetoothHCITransmitPowerLevelType", int
        ),
        "BluetoothAMPDiscoverResponseControllerStatus": NewType(
            "BluetoothAMPDiscoverResponseControllerStatus", int
        ),
        "BluetoothHCIVersions": NewType("BluetoothHCIVersions", int),
        "BluetoothHCIInquiryScanType": NewType("BluetoothHCIInquiryScanType", int),
        "IOBluetoothUserNotificationChannelDirection": NewType(
            "IOBluetoothUserNotificationChannelDirection", int
        ),
        "BluetoothL2CAPInformationType": NewType("BluetoothL2CAPInformationType", int),
        "BluetoothPageScanMode": NewType("BluetoothPageScanMode", int),
        "SDPServiceClasses": NewType("SDPServiceClasses", int),
        "BluetoothHCIRetransmissionEffortTypes": NewType(
            "BluetoothHCIRetransmissionEffortTypes", int
        ),
        "BluetoothCompanyIdentifers": NewType("BluetoothCompanyIdentifers", int),
        "BluetoothKeyType": NewType("BluetoothKeyType", int),
        "BluetoothLAP": NewType("BluetoothLAP", int),
        "BluetoothHCIPageScanEnableStates": NewType(
            "BluetoothHCIPageScanEnableStates", int
        ),
        "OBEXOpCodeResponseValues": NewType("OBEXOpCodeResponseValues", int),
        "IOBluetoothSMSMode": NewType("IOBluetoothSMSMode", int),
        "BluetoothHCIPageScanPeriodModes": NewType(
            "BluetoothHCIPageScanPeriodModes", int
        ),
        "BluetoothHCIErroneousDataReporting": NewType(
            "BluetoothHCIErroneousDataReporting", int
        ),
        "BluetoothAMPGetInfoResponseStatus": NewType(
            "BluetoothAMPGetInfoResponseStatus", int
        ),
        "BluetoothKeyFlag": NewType("BluetoothKeyFlag", int),
        "OBEXVersions": NewType("OBEXVersions", int),
        "BluetoothL2CAPConfigurationResult": NewType(
            "BluetoothL2CAPConfigurationResult", int
        ),
        "BluetoothSDPPDUID": NewType("BluetoothSDPPDUID", int),
        "BluetoothLEScanDuplicateFilter": NewType(
            "BluetoothLEScanDuplicateFilter", int
        ),
        "BluetoothHCIFECRequired": NewType("BluetoothHCIFECRequired", int),
        "BluetoothLESecurityManagerPairingFailedReasonCode": NewType(
            "BluetoothLESecurityManagerPairingFailedReasonCode", int
        ),
        "BluetoothL2CAPSegmentationAndReassembly": NewType(
            "BluetoothL2CAPSegmentationAndReassembly", int
        ),
        "OBEXSessionEventTypes": NewType("OBEXSessionEventTypes", int),
        "BluetoothPacketType": NewType("BluetoothPacketType", int),
        "BluetoothHCIEventStatus": NewType("BluetoothHCIEventStatus", int),
        "BluetoothL2CAPCommandRejectReason": NewType(
            "BluetoothL2CAPCommandRejectReason", int
        ),
        "BluetoothAuthenticationRequirements": NewType(
            "BluetoothAuthenticationRequirements", int
        ),
        "BluetoothLEScanType": NewType("BluetoothLEScanType", int),
        "BluetoothHCIInquiryMode": NewType("BluetoothHCIInquiryMode", int),
        "BluetoothHCIPageScanPeriodMode": NewType(
            "BluetoothHCIPageScanPeriodMode", int
        ),
        "BluetoothLESecurityManagerCommandCode": NewType(
            "BluetoothLESecurityManagerCommandCode", int
        ),
        "OBEXPutFlagValues": NewType("OBEXPutFlagValues", int),
        "BluetoothRFCOMMLineStatus": NewType("BluetoothRFCOMMLineStatus", int),
        "BluetoothAllowRoleSwitch": NewType("BluetoothAllowRoleSwitch", int),
        "BluetoothHCISimplePairingModes": NewType(
            "BluetoothHCISimplePairingModes", int
        ),
        "BluetoothL2CAPConnectionResult": NewType(
            "BluetoothL2CAPConnectionResult", int
        ),
        "BluetoothHCIVendorCommandSelector": NewType(
            "BluetoothHCIVendorCommandSelector", int
        ),
        "FTSFileType": NewType("FTSFileType", int),
        "BluetoothAMPCommandRejectReason": NewType(
            "BluetoothAMPCommandRejectReason", int
        ),
        "BluetoothHCIReadStoredLinkKeysFlags": NewType(
            "BluetoothHCIReadStoredLinkKeysFlags", int
        ),
        "IOBluetoothL2CAPChannelEventType": NewType(
            "IOBluetoothL2CAPChannelEventType", int
        ),
        "BluetoothOOBDataPresence": NewType("BluetoothOOBDataPresence", int),
        "ProtocolParameters": NewType("ProtocolParameters", int),
        "BluetoothLEFeatureBits": NewType("BluetoothLEFeatureBits", int),
        "IOBluetoothHandsFreePDUMessageStatus": NewType(
            "IOBluetoothHandsFreePDUMessageStatus", int
        ),
        "BluetoothHCITimeoutValues": NewType("BluetoothHCITimeoutValues", int),
        "BluetoothHCIAFHChannelAssessmentMode": NewType(
            "BluetoothHCIAFHChannelAssessmentMode", int
        ),
        "BluetoothLESecurityManagerOOBData": NewType(
            "BluetoothLESecurityManagerOOBData", int
        ),
        "BluetoothHCICommandOpCode": NewType("BluetoothHCICommandOpCode", int),
        "BluetoothLEScan": NewType("BluetoothLEScan", int),
        "BluetoothHCILinkPolicySettingsValues": NewType(
            "BluetoothHCILinkPolicySettingsValues", int
        ),
        "OBEXTransportEventTypes": NewType("OBEXTransportEventTypes", int),
        "BluetoothHCIFlowControlState": NewType("BluetoothHCIFlowControlState", int),
        "BluetoothPageScanRepetitionMode": NewType(
            "BluetoothPageScanRepetitionMode", int
        ),
        "IOBluetoothHandsFreeAudioGatewayFeatures": NewType(
            "IOBluetoothHandsFreeAudioGatewayFeatures", int
        ),
        "SDPAttributeDeviceIdentificationRecord": NewType(
            "SDPAttributeDeviceIdentificationRecord", int
        ),
        "SDPAttributeIdentifierCodes": NewType("SDPAttributeIdentifierCodes", int),
        "BluetoothSimplePairingDebugModes": NewType(
            "BluetoothSimplePairingDebugModes", int
        ),
        "BluetoothHCIInquiryScanTypes": NewType("BluetoothHCIInquiryScanTypes", int),
        "BluetoothKeypressNotificationTypes": NewType(
            "BluetoothKeypressNotificationTypes", int
        ),
        "BluetoothEncryptionEnable": NewType("BluetoothEncryptionEnable", int),
        "BluetoothHCIDeleteStoredLinkKeyFlags": NewType(
            "BluetoothHCIDeleteStoredLinkKeyFlags", int
        ),
        "BluetoothL2CAPCommandCode": NewType("BluetoothL2CAPCommandCode", int),
        "BluetoothLESecurityManagerUserInputCapability": NewType(
            "BluetoothLESecurityManagerUserInputCapability", int
        ),
        "BluetoothLinkTypes": NewType("BluetoothLinkTypes", int),
        "BluetoothHCILoopbackMode": NewType("BluetoothHCILoopbackMode", int),
        "BluetoothHCIAuthenticationEnable": NewType(
            "BluetoothHCIAuthenticationEnable", int
        ),
        "BluetoothHCIPageScanType": NewType("BluetoothHCIPageScanType", int),
        "BluetoothL2CAPChannelID": NewType("BluetoothL2CAPChannelID", int),
        "BluetoothHCIPageScanMode": NewType("BluetoothHCIPageScanMode", int),
    }
)
misc.update({})
misc.update(
    {
        "kEncodingStringQuotedPrintable": b"QUOTED-PRINTABLE",
        "kEncodingStringBase64": b"BASE-64",
        "kNoNotifyProc": None,
        "kIOBluetoothDeviceNotificationNameDisconnected": "IOBluetoothDeviceDisconnected",
        "kIOBluetoothL2CAPChannelMaxAllowedIncomingMTU": "MaxAllowedIncomingMTU",
        "kCharsetStringUTF8": b"UTF-8",
        "kCharsetStringISO88591": b"CHARSET=ISO-8859-1",
        "kIOBluetoothDeviceNotificationNameConnected": "IOBluetoothDeviceConnected",
        "kIOBluetoothDeviceNameChangedNotification": "IOBluetoothDeviceNameChanged",
        "kBluetoothTargetDoesNotRespondToCallbackExceptionName": "BluetoothTargetDoesNotRespondToCallbackException",
        "kIOBluetoothDeviceInquiryInfoChangedNotification": "IOBluetoothDeviceInquiryInfoChanged",
        "kNoUserRefCon": None,
        "kIOBluetoothL2CAPChannelDesiredOutgoingMTU": "DesiredOutgoingMTU",
        "kEncodingString8Bit": b"8BIT",
        "kIOBluetoothDeviceServicesChangedNotification": "IOBluetoothDeviceServicesChanged",
    }
)
functions = {
    "OBEXAddApplicationParameterHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXCreateVEvent": (
        b"^{__CFData=}^tI^tI^tI^tI^tI^tI^tI^tI^tI",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"c_array_length_in_arg": 1, "type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                4: {"c_array_length_in_arg": 5, "type_modifier": "n"},
                6: {"c_array_length_in_arg": 7, "type_modifier": "n"},
                8: {"c_array_length_in_arg": 9, "type_modifier": "n"},
                10: {"c_array_length_in_arg": 11, "type_modifier": "n"},
                12: {"c_array_length_in_arg": 13, "type_modifier": "n"},
                14: {"c_array_length_in_arg": 15, "type_modifier": "n"},
                16: {"c_array_length_in_arg": 17, "type_modifier": "n"},
            },
        },
    ),
    "IOBluetoothNumberOfKeyboardHIDDevices": (b"q",),
    "OBEXCreateVCard": (
        b"^{__CFData=}^tI^tI^tI^tI^tI^tI^tI^tI^tI^tI^tI^tI^tI^tI",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"c_array_length_in_arg": 1, "type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                4: {"c_array_length_in_arg": 5, "type_modifier": "n"},
                6: {"c_array_length_in_arg": 7, "type_modifier": "n"},
                8: {"c_array_length_in_arg": 9, "type_modifier": "n"},
                10: {"c_array_length_in_arg": 11, "type_modifier": "n"},
                12: {"c_array_length_in_arg": 13, "type_modifier": "n"},
                14: {"c_array_length_in_arg": 15, "type_modifier": "n"},
                16: {"c_array_length_in_arg": 17, "type_modifier": "n"},
                18: {"c_array_length_in_arg": 19, "type_modifier": "n"},
                20: {"c_array_length_in_arg": 21, "type_modifier": "n"},
                22: {"c_array_length_in_arg": 23, "type_modifier": "n"},
                24: {"c_array_length_in_arg": 25, "type_modifier": "n"},
                26: {"c_array_length_in_arg": 27, "type_modifier": "n"},
            },
        },
    ),
    "OBEXSessionHasOpenOBEXConnection": (
        b"i^{OpaqueOBEXSessionRef=}^Z",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "OBEXAddNameHeader": (b"i^{__CFString=}^{__CFDictionary=}",),
    "IOBluetoothFindNumberOfRegistryEntriesOfClassName": (
        b"q^t",
        "",
        {"arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "IOBluetoothRemoveSCOAudioDevice": (b"i^{OpaqueIOBluetoothObjectRef=}",),
    "IOBluetoothNumberOfAvailableHIDDevices": (b"q",),
    "IOBluetoothNSStringFromDeviceAddress": (
        b"@^{BluetoothDeviceAddress=[6C]}",
        "",
        {"arguments": {0: {"type_modifier": "n"}}},
    ),
    "OBEXAddObjectClassHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXAddTime4ByteHeader": (b"iI^{__CFDictionary=}",),
    "IOBluetoothIsFileAppleDesignatedPIMData": (b"Z@",),
    "OBEXGetHeaders": (
        b"^{__CFDictionary=}^vQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXAddTimeISOHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXSessionGetAvailableCommandResponsePayloadLength": (
        b"i^{OpaqueOBEXSessionRef=}C^S",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "IOBluetoothRemoveIgnoredHIDDevice": (b"v^{OpaqueIOBluetoothObjectRef=}",),
    "IOBluetoothNumberOfTabletHIDDevices": (b"q",),
    "OBEXSessionDelete": (b"i^{OpaqueOBEXSessionRef=}",),
    "OBEXAddTargetHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXAddUserDefinedHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "IOBluetoothNSStringToDeviceAddress": (
        b"i@^{BluetoothDeviceAddress=[6C]}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "OBEXAddDescriptionHeader": (b"i^{__CFString=}^{__CFDictionary=}",),
    "OBEXAddAuthorizationResponseHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXSessionGetAvailableCommandPayloadLength": (
        b"i^{OpaqueOBEXSessionRef=}C^S",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "IOBluetoothL2CAPChannelRegisterForChannelCloseNotification": (
        b"^{OpaqueIOBluetoothObjectRef=}^{OpaqueIOBluetoothObjectRef=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{OpaqueIOBluetoothObjectRef=}"},
                            2: {"type": b"^{OpaqueIOBluetoothObjectRef=}"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "IOBluetoothNumberOfPointingHIDDevices": (b"q",),
    "OBEXAddWhoHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "IOBluetoothOBEXSessionCreateWithIOBluetoothSDPServiceRecordRef": (
        b"i^{OpaqueIOBluetoothObjectRef=}^^{OpaqueOBEXSessionRef=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "IOBluetoothOBEXSessionCreateWithIOBluetoothDeviceRefAndChannelNumber": (
        b"i^{OpaqueIOBluetoothObjectRef=}C^^{OpaqueOBEXSessionRef=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "OBEXAddLengthHeader": (b"iI^{__CFDictionary=}",),
    "OBEXAddBodyHeader": (
        b"i^vIZ^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "IOBluetoothOBEXSessionOpenTransportConnection": (
        b"i^{OpaqueOBEXSessionRef=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{OpaqueOBEXSessionRef=}"},
                            1: {"type": b"i"},
                            2: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "OBEXAddAuthorizationChallengeHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXAddHTTPHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "OBEXAddCountHeader": (b"iI^{__CFDictionary=}",),
    "OBEXAddConnectionIDHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "IOBluetoothNSStringFromDeviceAddressColon": (
        b"@^{BluetoothDeviceAddress=[6C]}",
        "",
        {"arguments": {0: {"type_modifier": "n"}}},
    ),
    "OBEXAddTypeHeader": (b"i^{__CFString=}^{__CFDictionary=}",),
    "OBEXSessionGetMaxPacketLength": (
        b"i^{OpaqueOBEXSessionRef=}^S",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "IOBluetoothAddSCOAudioDevice": (
        b"i^{OpaqueIOBluetoothObjectRef=}^{__CFDictionary=}",
    ),
    "IOBluetoothUserNotificationUnregister": (b"v^{OpaqueIOBluetoothObjectRef=}",),
    "OBEXHeadersToBytes": (b"^{__CFData=}^{__CFDictionary=}",),
    "OBEXAddByteSequenceHeader": (
        b"i^vI^{__CFDictionary=}",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "IOBluetoothIgnoreHIDDevice": (b"v^{OpaqueIOBluetoothObjectRef=}",),
    "IOBluetoothGetUniqueFileNameAndPath": (b"@@@",),
}
aliases = {
    "kBluetoothFeatureAFHClassificationSlave": "kBluetoothFeatureAFHClassificationPeripheral",
    "kBluetoothFeatureAFHCapableSlave": "kBluetoothFeatureAFHCapablePeripheral",
    "kBluetoothL2CAPQoSTypeDefault": "kBluetoothL2CAPQoSTypeBestEffort",
    "kBluetoothRoleBecomeMaster": "kBluetoothRoleBecomeCentral",
    "kBluetoothRoleRemainSlave": "kBluetoothRoleRemainPeripheral",
    "kBluetoothHCIEventMaskDefault": "kBluetoothHCIEventMaskAll",
    "IOBluetoothL2CAPChannelRef": "IOBluetoothObjectRef",
    "IOBluetoothUserNotificationRef": "IOBluetoothObjectRef",
    "IOBluetoothSDPUUIDRef": "IOBluetoothObjectRef",
    "BluetoothLEScanFilterWhitelist": "BluetoothLEScanFilterSafelist",
    "kBluetoothHCICommandSetConnectionlessSlaveBroadcastReceive": "kBluetoothHCICommandSetConnectionlessPeripheralBroadcastReceive",
    "kBluetoothL2CAPFlushTimeoutDefault": "kBluetoothL2CAPFlushTimeoutForever",
    "kEnableMasterSlaveSwitch": "kEnableCentralPeripheralSwitch",
    "IOBluetoothSDPDataElementRef": "IOBluetoothObjectRef",
    "kBluetoothLEFeatureSlaveInitiatedFeaturesExchange": "kBluetoothLEFeaturePeripheralInitiatedFeaturesExchange",
    "kBluetoothLEMaxTXTimeDefault": "kBluetoothL2CAPMTULowEnergyDefault",
    "IOBluetoothRFCOMMChannelRef": "IOBluetoothObjectRef",
    "IOBluetoothSDPServiceRecordRef": "IOBluetoothObjectRef",
    "kBluetoothHCICommandSetConnectionlessSlaveBroadcast": "kBluetoothHCICommandSetConnectionlessPeripheralBroadcast",
    "BluetoothHCILESupportedFeatures": "BluetoothHCISupportedFeatures",
    "kBluetoothHCIExtendedInquiryResponseDataTypeSlaveConnectionIntervalRange": "kBluetoothHCIExtendedInquiryResponseDataTypePeripheralConnectionIntervalRange",
    "kBluetoothL2CAPMTULowEnergyMax": "kBluetoothLETXOctetsMax",
    "kBluetoothHCICommandSetConnectionlessSlaveBroadcastData": "kBluetoothHCICommandSetConnectionlessPeripheralBroadcastData",
    "kBluetoothHCISlaveRole": "kBluetoothHCIPeripheralRole",
    "kBluetoothHCIMasterRole": "kBluetoothHCICentralRole",
    "BluetoothHCILEUsedFeatures": "BluetoothHCISupportedFeatures",
    "kBluetoothL2CAPMTULowEnergyDefault": "kBluetoothLETXOctetsMin",
    "IOBluetoothDeviceRef": "IOBluetoothObjectRef",
}
misc.update(
    {
        "IOBluetoothObjectRef": objc.createOpaquePointerType(
            "IOBluetoothObjectRef", b"^{OpaqueIOBluetoothObjectRef=}"
        ),
        "OBEXSessionRef": objc.createOpaquePointerType(
            "OBEXSessionRef", b"^{OpaqueOBEXSessionRef=}"
        ),
    }
)
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"IOBluetoothDevice", b"isConnected", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothDevice", b"isFavorite", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothDevice", b"isHandsFreeAudioGateway", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothDevice", b"isHandsFreeDevice", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothDevice", b"isIncoming", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothDevice", b"isPaired", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothDevice",
        b"openConnection:withPageTimeout:authenticationRequired:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"openL2CAPChannel:findExisting:newChannel:",
        {"arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"openL2CAPChannelAsync:withPSM:delegate:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"openL2CAPChannelSync:withPSM:delegate:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"openRFCOMMChannel:channel:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"openRFCOMMChannelAsync:withChannelID:delegate:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"openRFCOMMChannelSync:withChannelID:delegate:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"IOBluetoothDevice",
        b"sendL2CAPEchoRequest:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"IOBluetoothDeviceInquiry",
        b"setUpdateNewDeviceNames:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"IOBluetoothDeviceInquiry", b"updateNewDeviceNames", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothDevicePair",
        b"replyPINCode:PINCode:",
        {"arguments": {3: {"type_modifier": b"n"}}},
    )
    r(
        b"IOBluetoothDevicePair",
        b"replyUserConfirmation:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"IOBluetoothHandsFree", b"isConnected", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothHandsFree", b"isInputMuted", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothHandsFree", b"isOutputMuted", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothHandsFree", b"isSCOConnected", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothHandsFree", b"isSMSEnabled", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothHandsFree", b"setInputMuted:", {"arguments": {2: {"type": b"Z"}}})
    r(b"IOBluetoothHandsFree", b"setOutputMuted:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"IOBluetoothHandsFreeAudioGateway",
        b"sendResponse:withOK:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"IOBluetoothL2CAPChannel", b"isIncoming", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothL2CAPChannel",
        b"writeAsync:length:refcon:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"IOBluetoothL2CAPChannel",
        b"writeAsyncTrap:length:refcon:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"IOBluetoothL2CAPChannel",
        b"writeSync:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"IOBluetoothOBEXSession",
        b"hasOpenTransportConnection",
        {"retval": {"type": b"Z"}},
    )
    r(b"IOBluetoothOBEXSession", b"isSessionTargetAMac", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothOBEXSession",
        b"sendDataToTransport:dataLength:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"IOBluetoothRFCOMMChannel", b"isIncoming", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothRFCOMMChannel", b"isOpen", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothRFCOMMChannel", b"isTransmissionPaused", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothRFCOMMChannel",
        b"write:length:sleep:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": b"Z"},
            }
        },
    )
    r(
        b"IOBluetoothRFCOMMChannel",
        b"writeAsync:length:refcon:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"IOBluetoothRFCOMMChannel",
        b"writeSimple:length:sleep:bytesSent:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": b"Z"},
                5: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"IOBluetoothRFCOMMChannel",
        b"writeSync:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"IOBluetoothSDPDataElement", b"containsDataElement:", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothSDPDataElement", b"containsValue:", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothSDPServiceRecord",
        b"hasServiceFromArray:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"IOBluetoothSDPServiceRecord",
        b"matchesSearchArray:",
        {"retval": {"type": b"Z"}},
    )
    r(b"IOBluetoothSDPServiceRecord", b"matchesUUID16:", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothSDPServiceRecord", b"matchesUUIDArray:", {"retval": {"type": b"Z"}})
    r(b"IOBluetoothSDPUUID", b"isEqualToUUID:", {"retval": {"type": b"Z"}})
    r(
        b"IOBluetoothSDPUUID",
        b"uuidWithBytes:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addApplicationParameterHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addAuthorizationChallengeHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addAuthorizationResponseHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addBodyHeader:length:endOfBody:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": b"Z"},
            }
        },
    )
    r(
        b"NSMutableDictionary",
        b"addByteSequenceHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addConnectionIDHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addHTTPHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addImageDescriptorHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addObjectClassHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addTargetHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addTimeISOHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addUserDefinedHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"addWhoHeader:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"dictionaryWithOBEXHeadersData:headersDataSize:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableDictionary",
        b"withOBEXHeadersData:headersDataSize:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSObject",
        b"connectionComplete:status:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"deviceInquiryComplete:error:aborted:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}, 4: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"deviceInquiryDeviceFound:device:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"deviceInquiryDeviceNameUpdated:device:devicesRemaining:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"I"}},
        },
    )
    r(
        b"NSObject",
        b"deviceInquiryStarted:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"deviceInquiryUpdatingDeviceNamesStarted:devicesRemaining:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"I"}},
        },
    )
    r(
        b"NSObject",
        b"devicePairingConnected:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"devicePairingConnecting:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"devicePairingFinished:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"devicePairingPINCodeRequest:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"devicePairingStarted:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"devicePairingUserConfirmationRequest:numericValue:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"I"}},
        },
    )
    r(
        b"NSObject",
        b"devicePairingUserPasskeyNotification:passkey:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"I"}},
        },
    )
    r(
        b"NSObject",
        b"deviceSimplePairingComplete:status:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"C"}},
        },
    )
    r(
        b"NSObject",
        b"fileTransferServicesAbortComplete:error:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesConnectionComplete:error:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesCopyRemoteFileComplete:error:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesCopyRemoteFileProgress:transferProgress:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesCreateFolderComplete:error:folder:",
        {
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileTransferServicesDisconnectionComplete:error:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesFilePreparationComplete:error:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesPathChangeComplete:error:finalPath:",
        {
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileTransferServicesRemoveItemComplete:error:removedItem:",
        {
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileTransferServicesRetrieveFolderListingComplete:error:listing:",
        {
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileTransferServicesSendFileComplete:error:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}}},
    )
    r(
        b"NSObject",
        b"fileTransferServicesSendFileProgress:transferProgress:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handsFree:batteryCharge:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:callHoldState:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:callSetupMode:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:connected:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:currentCall:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:disconnected:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:hangup:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:incomingCallFrom:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:incomingSMS:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:isCallActive:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:isRoaming:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:isServiceAvailable:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:redial:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:ringAttempt:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:scoConnectionClosed:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:scoConnectionOpened:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:signalStrength:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:subscriberNumber:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handsFree:unhandledResultCode:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"l2capChannelClosed:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"l2capChannelData:data:length:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"l2capChannelOpenComplete:status:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"l2capChannelQueueSpaceAvailable:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"l2capChannelReconfigured:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"l2capChannelWriteComplete:refcon:status:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^v"}, 4: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"readLinkQualityForDeviceComplete:device:info:error:",
        {
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^{BluetoothHCILinkQualityInfo=SC}"},
                5: {"type": b"i"},
            },
        },
    )
    r(
        b"NSObject",
        b"readRSSIForDeviceComplete:device:info:error:",
        {
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^{BluetoothHCIRSSIInfo=Sc}"},
                5: {"type": b"i"},
            },
        },
    )
    r(
        b"NSObject",
        b"remoteNameRequestComplete:status:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"rfcommChannelClosed:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"rfcommChannelControlSignalsChanged:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"rfcommChannelData:data:length:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"rfcommChannelFlowControlChanged:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"rfcommChannelOpenComplete:status:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"rfcommChannelQueueSpaceAvailable:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"rfcommChannelWriteComplete:refcon:status:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^v"}, 4: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"rfcommChannelWriteComplete:refcon:status:bytesWritten:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^v"},
                4: {"type": b"i"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"sdpQueryComplete:status:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"i"}},
        },
    )
    r(b"OBEXFileTransferServices", b"isBusy", {"retval": {"type": b"Z"}})
    r(b"OBEXFileTransferServices", b"isConnected", {"retval": {"type": b"Z"}})
    r(
        b"OBEXSession",
        b"OBEXAbort:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"OBEXSession",
        b"OBEXAbortResponse:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"OBEXSession",
        b"OBEXConnect:maxPacketLength:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {4: {"type_modifier": b"n", "c_array_length_in_arg": 5}}},
    )
    r(
        b"OBEXSession",
        b"OBEXConnectResponse:flags:maxPacketLength:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {5: {"type_modifier": b"n", "c_array_length_in_arg": 6}}},
    )
    r(
        b"OBEXSession",
        b"OBEXDisconnect:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"OBEXSession",
        b"OBEXDisconnectResponse:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"OBEXSession",
        b"OBEXGet:headers:headersLength:eventSelector:selectorTarget:refCon:",
        {
            "arguments": {
                2: {"type": b"Z"},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            }
        },
    )
    r(
        b"OBEXSession",
        b"OBEXGetResponse:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"OBEXSession",
        b"OBEXPut:headersData:headersDataLength:bodyData:bodyDataLength:eventSelector:selectorTarget:refCon:",
        {
            "arguments": {
                2: {"type": b"Z"},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"n", "c_array_length_in_arg": 6},
            }
        },
    )
    r(
        b"OBEXSession",
        b"OBEXPutResponse:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"OBEXSession",
        b"OBEXSetPath:constants:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {4: {"type_modifier": b"n", "c_array_length_in_arg": 5}}},
    )
    r(
        b"OBEXSession",
        b"OBEXSetPathResponse:optionalHeaders:optionalHeadersLength:eventSelector:selectorTarget:refCon:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(b"OBEXSession", b"hasOpenOBEXConnection", {"retval": {"type": b"Z"}})
    r(b"OBEXSession", b"hasOpenTransportConnection", {"retval": {"type": b"Z"}})
    r(
        b"OBEXSession",
        b"sendDataToTransport:dataLength:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("IOBluetoothDeviceInquiry", b"initWithDelegate:")
objc.registerNewKeywordsFromSelector(
    "IOBluetoothHandsFree", b"initWithDevice:delegate:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothHandsFreeAudioGateway", b"initWithDevice:delegate:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothHandsFreeDevice", b"initWithDevice:delegate:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothOBEXSession", b"initWithDevice:channelID:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothOBEXSession",
    b"initWithIncomingRFCOMMChannel:eventSelector:selectorTarget:refCon:",
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothOBEXSession", b"initWithSDPServiceRecord:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothSDPDataElement", b"initWithElementValue:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothSDPDataElement", b"initWithType:sizeDescriptor:size:value:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothSDPServiceAttribute", b"initWithID:attributeElement:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothSDPServiceAttribute", b"initWithID:attributeElementValue:"
)
objc.registerNewKeywordsFromSelector(
    "IOBluetoothSDPServiceRecord", b"initWithServiceDictionary:device:"
)
objc.registerNewKeywordsFromSelector("IOBluetoothSDPUUID", b"initWithUUID16:")
objc.registerNewKeywordsFromSelector("IOBluetoothSDPUUID", b"initWithUUID32:")
objc.registerNewKeywordsFromSelector(
    "OBEXFileTransferServices", b"initWithOBEXSession:"
)
protocols = {
    "IOBluetoothHostControllerDelegate": objc.informal_protocol(
        "IOBluetoothHostControllerDelegate",
        [
            objc.selector(
                None,
                b"readLinkQualityForDeviceComplete:device:info:error:",
                b"v@:@@^{BluetoothHCILinkQualityInfo=SC}i",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"readRSSIForDeviceComplete:device:info:error:",
                b"v@:@@^{BluetoothHCIRSSIInfo=Sc}i",
                isRequired=False,
            ),
        ],
    ),
    "OBEXFileTransferServicesDelegate": objc.informal_protocol(
        "OBEXFileTransferServicesDelegate",
        [
            objc.selector(
                None,
                b"fileTransferServicesFilePreparationComplete:error:",
                b"v@:@i",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesCopyRemoteFileComplete:error:",
                b"v@:@i",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesDisconnectionComplete:error:",
                b"v@:@i",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesSendFileProgress:transferProgress:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesCopyRemoteFileProgress:transferProgress:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesSendFileComplete:error:",
                b"v@:@i",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesRetrieveFolderListingComplete:error:listing:",
                b"v@:@i@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesPathChangeComplete:error:finalPath:",
                b"v@:@i@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesAbortComplete:error:",
                b"v@:@i",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesRemoveItemComplete:error:removedItem:",
                b"v@:@i@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesCreateFolderComplete:error:folder:",
                b"v@:@i@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"fileTransferServicesConnectionComplete:error:",
                b"v@:@i",
                isRequired=False,
            ),
        ],
    ),
}
expressions = {}

# END OF FILE
