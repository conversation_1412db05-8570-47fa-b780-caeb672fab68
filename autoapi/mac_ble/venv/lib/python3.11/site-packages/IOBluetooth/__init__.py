"""
Python mapping for the IOBluetooth framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import AppKit
    import objc
    from . import _metadata, _funcmacros, _IOBluetooth

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="IOBluetooth",
        frameworkIdentifier="com.apple.Bluetooth",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/IOBluetooth.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(
            _IOBluetooth,
            _funcmacros,
            AppKit,
        ),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    del sys.modules["IOBluetooth._metadata"]


globals().pop("_setup")()
