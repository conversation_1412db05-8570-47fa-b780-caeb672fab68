# This file is generated by objective.metadata
#
# Last update: Thu Nov 14 08:55:50 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$UTTagClassFilenameExtension$UTTagClassMIMEType$UTType3DContent$UTTypeAHAP$UTTypeAIFF$UTTypeARReferenceObject$UTTypeAVI$UTTypeAliasFile$UTTypeAppleArchive$UTTypeAppleProtectedMPEG4Audio$UTTypeAppleProtectedMPEG4Video$UTTypeAppleScript$UTTypeApplication$UTTypeApplicationBundle$UTTypeApplicationExtension$UTTypeArchive$UTTypeAssemblyLanguageSource$UTTypeAudio$UTTypeAudiovisualContent$UTTypeBMP$UTTypeBZ2$UTTypeBinaryPropertyList$UTTypeBookmark$UTTypeBundle$UTTypeCHeader$UTTypeCPlusPlusHeader$UTTypeCPlusPlusSource$UTTypeCSS$UTTypeCSource$UTTypeCalendarEvent$UTTypeCommaSeparatedText$UTTypeCompositeContent$UTTypeContact$UTTypeContent$UTTypeDNG$UTTypeData$UTTypeDatabase$UTTypeDelimitedText$UTTypeDirectory$UTTypeDiskImage$UTTypeEPUB$UTTypeEXE$UTTypeEXR$UTTypeEmailMessage$UTTypeExecutable$UTTypeFileURL$UTTypeFlatRTFD$UTTypeFolder$UTTypeFont$UTTypeFramework$UTTypeGIF$UTTypeGZIP$UTTypeGeoJSON$UTTypeHEIC$UTTypeHEICS$UTTypeHEIF$UTTypeHTML$UTTypeICNS$UTTypeICO$UTTypeImage$UTTypeInternetLocation$UTTypeInternetShortcut$UTTypeItem$UTTypeJPEG$UTTypeJPEGXL$UTTypeJSON$UTTypeJavaScript$UTTypeLinkPresentationMetadata$UTTypeLivePhoto$UTTypeLog$UTTypeM3UPlaylist$UTTypeMIDI$UTTypeMP3$UTTypeMPEG$UTTypeMPEG2TransportStream$UTTypeMPEG2Video$UTTypeMPEG4Audio$UTTypeMPEG4Movie$UTTypeMakefile$UTTypeMessage$UTTypeMountPoint$UTTypeMovie$UTTypeOSAScript$UTTypeOSAScriptBundle$UTTypeObjectiveCPlusPlusSource$UTTypeObjectiveCSource$UTTypePDF$UTTypePHPScript$UTTypePKCS12$UTTypePNG$UTTypePackage$UTTypePerlScript$UTTypePlainText$UTTypePlaylist$UTTypePluginBundle$UTTypePresentation$UTTypePropertyList$UTTypePythonScript$UTTypeQuickLookGenerator$UTTypeQuickTimeMovie$UTTypeRAWImage$UTTypeRTF$UTTypeRTFD$UTTypeRealityFile$UTTypeResolvable$UTTypeRubyScript$UTTypeSVG$UTTypeSceneKitScene$UTTypeScript$UTTypeShellScript$UTTypeSourceCode$UTTypeSpotlightImporter$UTTypeSpreadsheet$UTTypeSwiftSource$UTTypeSymbolicLink$UTTypeSystemPreferencesPane$UTTypeTIFF$UTTypeTabSeparatedText$UTTypeTarArchive$UTTypeText$UTTypeToDoItem$UTTypeURL$UTTypeURLBookmarkData$UTTypeUSD$UTTypeUSDZ$UTTypeUTF16ExternalPlainText$UTTypeUTF16PlainText$UTTypeUTF8PlainText$UTTypeUTF8TabSeparatedText$UTTypeUnixExecutable$UTTypeVCard$UTTypeVideo$UTTypeVolume$UTTypeWAV$UTTypeWebArchive$UTTypeWebP$UTTypeX509Certificate$UTTypeXML$UTTypeXMLPropertyList$UTTypeXPCService$UTTypeYAML$UTTypeZIP$"""
enums = """$$"""
misc.update({})
misc.update({})
misc.update({})
aliases = {"UT_AVAILABLE_END": "API_AVAILABLE_END"}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSItemProvider",
        b"initWithContentsOfURL:contentType:openInPlace:coordinated:visibility:",
        {"arguments": {4: {"type": b"Z"}, 5: {"type": b"Z"}}},
    )
    r(
        b"NSItemProvider",
        b"loadDataRepresentationForContentType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"loadFileRepresentationForContentType:openInPlace:completionHandler:",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSItemProvider",
        b"registerDataRepresentationForContentType:visibility:loadHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"registerFileRepresentationForContentType:visibility:openInPlace:loadHandler:",
        {
            "arguments": {
                4: {"type": b"Z"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@?"}},
                    }
                },
            }
        },
    )
    r(b"UTType", b"conformsToType:", {"retval": {"type": b"Z"}})
    r(b"UTType", b"isDeclared", {"retval": {"type": b"Z"}})
    r(b"UTType", b"isDynamic", {"retval": {"type": b"Z"}})
    r(b"UTType", b"isPublicType", {"retval": {"type": b"Z"}})
    r(b"UTType", b"isSubtypeOfType:", {"retval": {"type": b"Z"}})
    r(b"UTType", b"isSupertypeOfType:", {"retval": {"type": b"Z"}})
    r(b"null", b"conformsToType:", {"retval": {"type": b"Z"}})
    r(b"null", b"isSubtypeOfType:", {"retval": {"type": b"Z"}})
    r(b"null", b"isSupertypeOfType:", {"retval": {"type": b"Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "NSItemProvider",
    b"initWithContentsOfURL:contentType:openInPlace:coordinated:visibility:",
)
expressions = {}

# END OF FILE
