Metal/_Metal.cpython-311-darwin.so,sha256=yefmXLpiz2_9bRldJ_QzZEYdPWlCrQkemPG8u8m0BiY,184112
Metal/__init__.py,sha256=FC6IC95ck74LODZ-ZRmSfNi8m54lEa8aTLUbt1mUuvg,911
Metal/__pycache__/__init__.cpython-311.pyc,,
Metal/__pycache__/_metadata.cpython-311.pyc,,
Metal/_inlines.cpython-311-darwin.so,sha256=ZCafyOfX1itkXX74G4vi3RYiJedZM94Yy0UrS9LtynU,92312
Metal/_metadata.py,sha256=DVvpd7BYwy18I8BTJrHZ2Xe0Ep2g7X2cj5GS7vJJFwI,201991
pyobjc_framework_Metal-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_Metal-11.0.dist-info/METADATA,sha256=6X0RHDoHGiNytADNXOhx1F9R7Sb06VJuwKugLe9PhMQ,2227
pyobjc_framework_Metal-11.0.dist-info/RECORD,,
pyobjc_framework_Metal-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_Metal-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_Metal-11.0.dist-info/top_level.txt,sha256=ZhevtredTnEoMMZpTESQ7GI824pUv4TmBPQxBR7eFQs,6
