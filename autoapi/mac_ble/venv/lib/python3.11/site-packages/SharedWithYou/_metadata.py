# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:20:07 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = (
    """$NSPasteboardTypeCollaborationMetadata$SWCollaborationMetadataTypeIdentifier$"""
)
enums = """$SWAttributionViewBackgroundStyleColor@1$SWAttributionViewBackgroundStyleDefault@0$SWAttributionViewBackgroundStyleMaterial@2$SWAttributionViewDisplayContextDetail@1$SWAttributionViewDisplayContextSummary@0$SWAttributionViewHorizontalAlignmentCenter@2$SWAttributionViewHorizontalAlignmentDefault@0$SWAttributionViewHorizontalAlignmentLeading@1$SWAttributionViewHorizontalAlignmentTrailing@3$SWHighlightCenterErrorCodeAccessDenied@3$SWHighlightCenterErrorCodeInternalError@1$SWHighlightCenterErrorCodeInvalidURL@2$SWHighlightCenterErrorCodeNoError@0$SWHighlightChangeEventTriggerComment@2$SWHighlightChangeEventTriggerEdit@1$SWHighlightMembershipEventTriggerAddedCollaborator@1$SWHighlightMembershipEventTriggerRemovedCollaborator@2$SWHighlightPersistenceEventTriggerCreated@1$SWHighlightPersistenceEventTriggerDeleted@2$SWHighlightPersistenceEventTriggerMoved@4$SWHighlightPersistenceEventTriggerRenamed@3$"""
misc.update(
    {
        "SWAttributionViewHorizontalAlignment": NewType(
            "SWAttributionViewHorizontalAlignment", int
        ),
        "SWHighlightChangeEventTrigger": NewType("SWHighlightChangeEventTrigger", int),
        "SWAttributionViewBackgroundStyle": NewType(
            "SWAttributionViewBackgroundStyle", int
        ),
        "SWAttributionViewDisplayContext": NewType(
            "SWAttributionViewDisplayContext", int
        ),
        "SWHighlightCenterErrorCode": NewType("SWHighlightCenterErrorCode", int),
        "SWHighlightPersistenceEventTrigger": NewType(
            "SWHighlightPersistenceEventTrigger", int
        ),
        "SWHighlightMembershipEventTrigger": NewType(
            "SWHighlightMembershipEventTrigger", int
        ),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSObject",
        b"collaborationViewDidDismissPopover:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"collaborationViewShouldPresentPopover:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"collaborationViewWillPresentPopover:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"highlightCenterHighlightsDidChange:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"highlightURL", {"required": True, "retval": {"type": b"@"}})
    r(b"SWAttributionView", b"enablesMarquee", {"retval": {"type": b"Z"}})
    r(b"SWAttributionView", b"setEnablesMarquee:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"SWCollaborationView",
        b"dismissPopover:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"SWCollaborationView",
        b"setShowManageButton:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"SWHighlightCenter",
        b"collaborationHighlightForIdentifier:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"SWHighlightCenter",
        b"getCollaborationHighlightForURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SWHighlightCenter",
        b"getHighlightForURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SWHighlightCenter",
        b"getSignedIdentityProofForCollaborationHighlight:usingData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"SWHighlightCenter",
        b"isSystemCollaborationSupportAvailable",
        {"retval": {"type": b"Z"}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("SWCollaborationView", b"initWithItemProvider:")
objc.registerNewKeywordsFromSelector(
    "SWHighlightChangeEvent", b"initWithHighlight:trigger:"
)
objc.registerNewKeywordsFromSelector(
    "SWHighlightMembershipEvent", b"initWithHighlight:trigger:"
)
objc.registerNewKeywordsFromSelector(
    "SWHighlightMentionEvent", b"initWithHighlight:mentionedPersonCloudKitShareHandle:"
)
objc.registerNewKeywordsFromSelector(
    "SWHighlightMentionEvent", b"initWithHighlight:mentionedPersonIdentity:"
)
objc.registerNewKeywordsFromSelector(
    "SWHighlightPersistenceEvent", b"initWithHighlight:trigger:"
)
expressions = {}

# END OF FILE
