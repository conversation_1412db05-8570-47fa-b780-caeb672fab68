IOBluetooth/_IOBluetooth.cpython-311-darwin.so,sha256=uMUT-s_86pWjVmWJWUY7Iho2SFWW3mgVtC2HguvXoIo,87944
IOBluetooth/__init__.py,sha256=kk7-Rl6IljADnFk4qzzmzdBGfyi9ZkppoaH6acPGWRQ,940
IOBluetooth/__pycache__/__init__.cpython-311.pyc,,
IOBluetooth/__pycache__/_funcmacros.cpython-311.pyc,,
IOBluetooth/__pycache__/_metadata.cpython-311.pyc,,
IOBluetooth/_funcmacros.py,sha256=wiNJeHoJRtFg4J-3Ic-AtH0zt00N2zjwT0GllA45QlY,3252
IOBluetooth/_metadata.py,sha256=oapk7evuSVCrSR79_3XgQvXsad7haxWIa49i8iQR3U0,185313
pyobjc_framework_IOBluetooth-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_IOBluetooth-11.0.dist-info/METADATA,sha256=CqZiwbmTJk8fYyydEjr3j_6MkalpNgrNsiNB14ZA1OU,2241
pyobjc_framework_IOBluetooth-11.0.dist-info/RECORD,,
pyobjc_framework_IOBluetooth-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_IOBluetooth-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_IOBluetooth-11.0.dist-info/top_level.txt,sha256=V4Bs0Vg16C_-dAIdULu4pOo1CAVD8pAiw0MucUWJKmg,12
