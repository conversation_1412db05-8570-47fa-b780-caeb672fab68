PushKit/_PushKit.cpython-311-darwin.so,sha256=YqofervKPeyk_tJrrKO2OOqFwi20aamGzdC98jd70ds,68096
PushKit/__init__.py,sha256=QSVHZXITYWsnvSISAWQD2rwrrCxxQmS4jv-FQzoXjvg,986
PushKit/__pycache__/__init__.cpython-311.pyc,,
PushKit/__pycache__/_metadata.cpython-311.pyc,,
PushKit/_metadata.py,sha256=_7rQqwmLpwhbKRLhltHnPAP9FBdVJAJHGehOFnmjpDY,2193
pyobjc_framework_PushKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_PushKit-11.0.dist-info/METADATA,sha256=aFHn9jynPXlGF9PoXMsERVuYEwVhP5i1lHDifgdRnuk,2251
pyobjc_framework_PushKit-11.0.dist-info/RECORD,,
pyobjc_framework_PushKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_PushKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_PushKit-11.0.dist-info/top_level.txt,sha256=vv9fC49l71qtQZZYbxjV9FacD8_CHgWl7ETxk6vEt1M,8
