CoreHaptics/__init__.py,sha256=_9yQ8u88XPyQJi8pWE6EKrhY2aQcLupkfDiLI9ZMtq8,1234
CoreHaptics/__pycache__/__init__.cpython-311.pyc,,
CoreHaptics/__pycache__/_metadata.cpython-311.pyc,,
CoreHaptics/_metadata.py,sha256=nAAAZmLC7lCJWrnMeT__HtOiz2N6jpeir1WxKZdxYPk,15752
pyobjc_framework_CoreHaptics-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_CoreHaptics-11.0.dist-info/METADATA,sha256=uhkvFGQOefTVno-vL-VEeToMkkXoPnHpqitNUwh2FJM,2495
pyobjc_framework_CoreHaptics-11.0.dist-info/RECORD,,
pyobjc_framework_CoreHaptics-11.0.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
pyobjc_framework_CoreHaptics-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_CoreHaptics-11.0.dist-info/top_level.txt,sha256=Eo7NHy2ObKy4wiG-326QZq5UG2NJzeNk5Ty_rnYUAsY,12
