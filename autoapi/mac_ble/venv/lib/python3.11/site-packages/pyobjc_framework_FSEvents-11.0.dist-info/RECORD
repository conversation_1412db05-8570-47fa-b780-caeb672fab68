FSEvents/__init__.py,sha256=EYbw33ZpyHK6jCeimbOJS5atCdICcFwLhTv2nIl9-Rc,901
FSEvents/__pycache__/__init__.cpython-311.pyc,,
FSEvents/__pycache__/_metadata.cpython-311.pyc,,
FSEvents/_callbacks.cpython-311-darwin.so,sha256=eF23W7qMSpTTJmEr3mcKDTsdyjn-nmm3HByXf9tbZK0,85944
FSEvents/_metadata.py,sha256=V5Hgj0ze4De0CnbF00XNThfSAQQV6lIKKkgmEsj1kFQ,4035
pyobjc_framework_FSEvents-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_FSEvents-11.0.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_FSEvents-11.0.dist-info/METADATA,sha256=8lka23oCLo9xFp4M8dn-0xAoxi4keUGXlQSgGEMTm0I,2398
pyobjc_framework_FSEvents-11.0.dist-info/RECORD,,
pyobjc_framework_FSEvents-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_FSEvents-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_FSEvents-11.0.dist-info/top_level.txt,sha256=9HjffDftElYLXQHzGKD5UKkw2oCM9sDY-7kOBHI0SzM,9
