# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 13:00:09 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$$"""
enums = """$$"""
misc.update({})
misc.update({})
misc.update({})
functions = {
    "DCSDictionaryGetTypeID": (
        b"l",
        "",
        {"comment": "Function not present in header files"},
    ),
    "DCSGetTermRangeInString": (b"{CFRange=qq}^{__DCSDictionary=}^{__CFString=}q",),
    "DCSCopyTextDefinition": (
        b"^{__CFString=}^{__DCSDictionary=}^{__CFString=}{CFRange=qq}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
}
cftypes = [("DCSDictionaryRef", b"^{__DCSDictionary=}", "DCSDictionaryGetTypeID", None)]
expressions = {}

# END OF FILE
