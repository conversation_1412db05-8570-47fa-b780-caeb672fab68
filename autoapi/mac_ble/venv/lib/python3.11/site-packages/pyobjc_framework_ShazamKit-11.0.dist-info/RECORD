ShazamKit/_ShazamKit.cpython-311-darwin.so,sha256=9-k65rmMfiYEJwlM17L2IrC_mM1V9cRI1xx36OntlY8,67992
ShazamKit/__init__.py,sha256=ziQ-aqOR2C8UtHzwxq5B70-QhviS6HayFvJkqRt5b3c,1290
ShazamKit/__pycache__/__init__.cpython-311.pyc,,
ShazamKit/__pycache__/_metadata.cpython-311.pyc,,
ShazamKit/_metadata.py,sha256=HKb-Qm81R3QjozOGlKcqLmvrH7i9TMN03eT2cGGGpKo,4847
pyobjc_framework_ShazamKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_ShazamKit-11.0.dist-info/METADATA,sha256=0p5wOMS5Nyjh6hJlZhikHKp19Ync3yoAY-fRxwpif0g,2242
pyobjc_framework_ShazamKit-11.0.dist-info/RECORD,,
pyobjc_framework_ShazamKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_ShazamKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_ShazamKit-11.0.dist-info/top_level.txt,sha256=zBdjblWqe7kDJu8fgLWqFg9HXjSPYgKtUXqHqFnin5Y,10
