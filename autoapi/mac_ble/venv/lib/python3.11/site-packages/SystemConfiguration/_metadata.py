# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:21:10 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$kCFErrorDomainSystemConfiguration$kCNNetworkInfoKeyBSSID$kCNNetworkInfoKeySSID$kCNNetworkInfoKeySSIDData$kSCBondStatusDeviceAggregationStatus$kSCBondStatusDeviceCollecting$kSCBondStatusDeviceDistributing$kSCCompAnyRegex$kSCCompGlobal$kSCCompHostNames$kSCCompInterface$kSCCompNetwork$kSCCompService$kSCCompSystem$kSCCompUsers$kSCDynamicStoreDomainFile$kSCDynamicStoreDomainPlugin$kSCDynamicStoreDomainPrefs$kSCDynamicStoreDomainSetup$kSCDynamicStoreDomainState$kSCDynamicStorePropNetInterfaces$kSCDynamicStorePropNetPrimaryInterface$kSCDynamicStorePropNetPrimaryService$kSCDynamicStorePropNetServiceIDs$kSCDynamicStorePropSetupCurrentSet$kSCDynamicStorePropSetupLastUpdated$kSCDynamicStoreUseSessionKeys$kSCEntNet6to4$kSCEntNetAirPort$kSCEntNetAppleTalk$kSCEntNetDHCP$kSCEntNetDNS$kSCEntNetEthernet$kSCEntNetFireWire$kSCEntNetIPSec$kSCEntNetIPv4$kSCEntNetIPv6$kSCEntNetInterface$kSCEntNetL2TP$kSCEntNetLink$kSCEntNetModem$kSCEntNetNetInfo$kSCEntNetPPP$kSCEntNetPPPSerial$kSCEntNetPPPoE$kSCEntNetPPTP$kSCEntNetProxies$kSCEntNetSMB$kSCEntUsersConsoleUser$kSCNetworkInterfaceIPv4@=^{__SCNetworkInterface=}$kSCNetworkInterfaceType6to4$kSCNetworkInterfaceTypeBluetooth$kSCNetworkInterfaceTypeBond$kSCNetworkInterfaceTypeEthernet$kSCNetworkInterfaceTypeFireWire$kSCNetworkInterfaceTypeIEEE80211$kSCNetworkInterfaceTypeIPSec$kSCNetworkInterfaceTypeIPv4$kSCNetworkInterfaceTypeIrDA$kSCNetworkInterfaceTypeL2TP$kSCNetworkInterfaceTypeModem$kSCNetworkInterfaceTypePPP$kSCNetworkInterfaceTypePPTP$kSCNetworkInterfaceTypeSerial$kSCNetworkInterfaceTypeVLAN$kSCNetworkInterfaceTypeWWAN$kSCNetworkProtocolTypeAppleTalk$kSCNetworkProtocolTypeDNS$kSCNetworkProtocolTypeIPv4$kSCNetworkProtocolTypeIPv6$kSCNetworkProtocolTypeProxies$kSCNetworkProtocolTypeSMB$kSCPrefCurrentSet$kSCPrefNetworkServices$kSCPrefSets$kSCPrefSystem$kSCPropInterfaceName$kSCPropMACAddress$kSCPropNet6to4Relay$kSCPropNetAirPortAllowNetCreation$kSCPropNetAirPortAuthPassword$kSCPropNetAirPortAuthPasswordEncryption$kSCPropNetAirPortJoinMode$kSCPropNetAirPortPowerEnabled$kSCPropNetAirPortPreferredNetwork$kSCPropNetAirPortSavePasswords$kSCPropNetAppleTalkComputerName$kSCPropNetAppleTalkComputerNameEncoding$kSCPropNetAppleTalkConfigMethod$kSCPropNetAppleTalkDefaultZone$kSCPropNetAppleTalkNetworkID$kSCPropNetAppleTalkNetworkRange$kSCPropNetAppleTalkNodeID$kSCPropNetAppleTalkSeedNetworkRange$kSCPropNetAppleTalkSeedZones$kSCPropNetDNSDomainName$kSCPropNetDNSOptions$kSCPropNetDNSSearchDomains$kSCPropNetDNSSearchOrder$kSCPropNetDNSServerAddresses$kSCPropNetDNSServerPort$kSCPropNetDNSServerTimeout$kSCPropNetDNSSortList$kSCPropNetDNSSupplementalMatchDomains$kSCPropNetDNSSupplementalMatchOrders$kSCPropNetEthernetMTU$kSCPropNetEthernetMediaOptions$kSCPropNetEthernetMediaSubType$kSCPropNetIPSecAuthenticationMethod$kSCPropNetIPSecConnectTime$kSCPropNetIPSecLocalCertificate$kSCPropNetIPSecLocalIdentifier$kSCPropNetIPSecLocalIdentifierType$kSCPropNetIPSecRemoteAddress$kSCPropNetIPSecSharedSecret$kSCPropNetIPSecSharedSecretEncryption$kSCPropNetIPSecStatus$kSCPropNetIPSecXAuthEnabled$kSCPropNetIPSecXAuthName$kSCPropNetIPSecXAuthPassword$kSCPropNetIPSecXAuthPasswordEncryption$kSCPropNetIPv4Addresses$kSCPropNetIPv4BroadcastAddresses$kSCPropNetIPv4ConfigMethod$kSCPropNetIPv4DHCPClientID$kSCPropNetIPv4DestAddresses$kSCPropNetIPv4Router$kSCPropNetIPv4SubnetMasks$kSCPropNetIPv6Addresses$kSCPropNetIPv6ConfigMethod$kSCPropNetIPv6DestAddresses$kSCPropNetIPv6Flags$kSCPropNetIPv6PrefixLength$kSCPropNetIPv6Router$kSCPropNetInterfaceDeviceName$kSCPropNetInterfaceHardware$kSCPropNetInterfaceSubType$kSCPropNetInterfaceSupportsModemOnHold$kSCPropNetInterfaceType$kSCPropNetInterfaces$kSCPropNetL2TPIPSecSharedSecret$kSCPropNetL2TPIPSecSharedSecretEncryption$kSCPropNetL2TPTransport$kSCPropNetLinkActive$kSCPropNetLinkDetaching$kSCPropNetLocalHostName$kSCPropNetModemAccessPointName$kSCPropNetModemConnectSpeed$kSCPropNetModemConnectionPersonality$kSCPropNetModemConnectionScript$kSCPropNetModemDataCompression$kSCPropNetModemDeviceContextID$kSCPropNetModemDeviceModel$kSCPropNetModemDeviceVendor$kSCPropNetModemDialMode$kSCPropNetModemErrorCorrection$kSCPropNetModemHoldCallWaitingAudibleAlert$kSCPropNetModemHoldDisconnectOnAnswer$kSCPropNetModemHoldEnabled$kSCPropNetModemHoldReminder$kSCPropNetModemHoldReminderTime$kSCPropNetModemNote$kSCPropNetModemPulseDial$kSCPropNetModemSpeaker$kSCPropNetModemSpeed$kSCPropNetNetInfoBindingMethods$kSCPropNetNetInfoBroadcastServerTag$kSCPropNetNetInfoServerAddresses$kSCPropNetNetInfoServerTags$kSCPropNetOverridePrimary$kSCPropNetPPPACSPEnabled$kSCPropNetPPPAuthEAPPlugins$kSCPropNetPPPAuthName$kSCPropNetPPPAuthPassword$kSCPropNetPPPAuthPasswordEncryption$kSCPropNetPPPAuthPrompt$kSCPropNetPPPAuthProtocol$kSCPropNetPPPCCPEnabled$kSCPropNetPPPCCPMPPE128Enabled$kSCPropNetPPPCCPMPPE40Enabled$kSCPropNetPPPCommAlternateRemoteAddress$kSCPropNetPPPCommConnectDelay$kSCPropNetPPPCommDisplayTerminalWindow$kSCPropNetPPPCommRedialCount$kSCPropNetPPPCommRedialEnabled$kSCPropNetPPPCommRedialInterval$kSCPropNetPPPCommRemoteAddress$kSCPropNetPPPCommTerminalScript$kSCPropNetPPPCommUseTerminalScript$kSCPropNetPPPConnectTime$kSCPropNetPPPDeviceLastCause$kSCPropNetPPPDialOnDemand$kSCPropNetPPPDisconnectOnFastUserSwitch$kSCPropNetPPPDisconnectOnIdle$kSCPropNetPPPDisconnectOnIdleTimer$kSCPropNetPPPDisconnectOnLogout$kSCPropNetPPPDisconnectOnSleep$kSCPropNetPPPDisconnectTime$kSCPropNetPPPIPCPCompressionVJ$kSCPropNetPPPIPCPUsePeerDNS$kSCPropNetPPPIdleReminder$kSCPropNetPPPIdleReminderTimer$kSCPropNetPPPLCPCompressionACField$kSCPropNetPPPLCPCompressionPField$kSCPropNetPPPLCPEchoEnabled$kSCPropNetPPPLCPEchoFailure$kSCPropNetPPPLCPEchoInterval$kSCPropNetPPPLCPMRU$kSCPropNetPPPLCPMTU$kSCPropNetPPPLCPReceiveACCM$kSCPropNetPPPLCPTransmitACCM$kSCPropNetPPPLastCause$kSCPropNetPPPLogfile$kSCPropNetPPPOverridePrimary$kSCPropNetPPPPlugins$kSCPropNetPPPRetryConnectTime$kSCPropNetPPPSessionTimer$kSCPropNetPPPStatus$kSCPropNetPPPUseSessionTimer$kSCPropNetPPPVerboseLogging$kSCPropNetProxiesExceptionsList$kSCPropNetProxiesExcludeSimpleHostnames$kSCPropNetProxiesFTPEnable$kSCPropNetProxiesFTPPassive$kSCPropNetProxiesFTPPort$kSCPropNetProxiesFTPProxy$kSCPropNetProxiesFTPUser$kSCPropNetProxiesGopherEnable$kSCPropNetProxiesGopherPort$kSCPropNetProxiesGopherProxy$kSCPropNetProxiesGopherUser$kSCPropNetProxiesHTTPEnable$kSCPropNetProxiesHTTPPort$kSCPropNetProxiesHTTPProxy$kSCPropNetProxiesHTTPSEnable$kSCPropNetProxiesHTTPSPort$kSCPropNetProxiesHTTPSProxy$kSCPropNetProxiesHTTPSUser$kSCPropNetProxiesHTTPUser$kSCPropNetProxiesProxyAutoConfigEnable$kSCPropNetProxiesProxyAutoConfigJavaScript$kSCPropNetProxiesProxyAutoConfigURLString$kSCPropNetProxiesProxyAutoDiscoveryEnable$kSCPropNetProxiesRTSPEnable$kSCPropNetProxiesRTSPPort$kSCPropNetProxiesRTSPProxy$kSCPropNetProxiesRTSPUser$kSCPropNetProxiesSOCKSEnable$kSCPropNetProxiesSOCKSPort$kSCPropNetProxiesSOCKSProxy$kSCPropNetProxiesSOCKSUser$kSCPropNetSMBNetBIOSName$kSCPropNetSMBNetBIOSNodeType$kSCPropNetSMBNetBIOSScope$kSCPropNetSMBWINSAddresses$kSCPropNetSMBWorkgroup$kSCPropNetServiceOrder$kSCPropSystemComputerName$kSCPropSystemComputerNameEncoding$kSCPropUserDefinedName$kSCPropUsersConsoleUserGID$kSCPropUsersConsoleUserName$kSCPropUsersConsoleUserUID$kSCPropVersion$kSCResvInactive$kSCResvLink$kSCValNetAirPortAuthPasswordEncryptionKeychain$kSCValNetAirPortJoinModeAutomatic$kSCValNetAirPortJoinModePreferred$kSCValNetAirPortJoinModeRanked$kSCValNetAirPortJoinModeRecent$kSCValNetAirPortJoinModeStrongest$kSCValNetAppleTalkConfigMethodNode$kSCValNetAppleTalkConfigMethodRouter$kSCValNetAppleTalkConfigMethodSeedRouter$kSCValNetIPSecAuthenticationMethodCertificate$kSCValNetIPSecAuthenticationMethodHybrid$kSCValNetIPSecAuthenticationMethodSharedSecret$kSCValNetIPSecLocalIdentifierTypeKeyID$kSCValNetIPSecSharedSecretEncryptionKeychain$kSCValNetIPSecXAuthPasswordEncryptionKeychain$kSCValNetIPSecXAuthPasswordEncryptionPrompt$kSCValNetIPv4ConfigMethodAutomatic$kSCValNetIPv4ConfigMethodBOOTP$kSCValNetIPv4ConfigMethodDHCP$kSCValNetIPv4ConfigMethodINFORM$kSCValNetIPv4ConfigMethodLinkLocal$kSCValNetIPv4ConfigMethodManual$kSCValNetIPv4ConfigMethodPPP$kSCValNetIPv6ConfigMethod6to4$kSCValNetIPv6ConfigMethodAutomatic$kSCValNetIPv6ConfigMethodLinkLocal$kSCValNetIPv6ConfigMethodManual$kSCValNetIPv6ConfigMethodRouterAdvertisement$kSCValNetInterfaceSubTypeL2TP$kSCValNetInterfaceSubTypePPPSerial$kSCValNetInterfaceSubTypePPPoE$kSCValNetInterfaceSubTypePPTP$kSCValNetInterfaceType6to4$kSCValNetInterfaceTypeEthernet$kSCValNetInterfaceTypeFireWire$kSCValNetInterfaceTypeIPSec$kSCValNetInterfaceTypePPP$kSCValNetL2TPIPSecSharedSecretEncryptionKeychain$kSCValNetL2TPTransportIP$kSCValNetL2TPTransportIPSec$kSCValNetModemDialModeIgnoreDialTone$kSCValNetModemDialModeManual$kSCValNetModemDialModeWaitForDialTone$kSCValNetNetInfoBindingMethodsBroadcast$kSCValNetNetInfoBindingMethodsDHCP$kSCValNetNetInfoBindingMethodsManual$kSCValNetNetInfoDefaultServerTag$kSCValNetPPPAuthPasswordEncryptionKeychain$kSCValNetPPPAuthPasswordEncryptionToken$kSCValNetPPPAuthPromptAfter$kSCValNetPPPAuthPromptBefore$kSCValNetPPPAuthProtocolCHAP$kSCValNetPPPAuthProtocolEAP$kSCValNetPPPAuthProtocolMSCHAP1$kSCValNetPPPAuthProtocolMSCHAP2$kSCValNetPPPAuthProtocolPAP$kSCValNetSMBNetBIOSNodeTypeBroadcast$kSCValNetSMBNetBIOSNodeTypeHybrid$kSCValNetSMBNetBIOSNodeTypeMixed$kSCValNetSMBNetBIOSNodeTypePeer$"""
enums = """$kSCBondStatusLinkInvalid@1$kSCBondStatusNoPartner@2$kSCBondStatusNotInActiveGroup@3$kSCBondStatusOK@0$kSCBondStatusUnknown@999$kSCNetworkConnectionConnected@2$kSCNetworkConnectionConnecting@1$kSCNetworkConnectionDisconnected@0$kSCNetworkConnectionDisconnecting@3$kSCNetworkConnectionInvalid@-1$kSCNetworkConnectionPPPAuthenticating@5$kSCNetworkConnectionPPPConnected@8$kSCNetworkConnectionPPPConnectingLink@2$kSCNetworkConnectionPPPDialOnTraffic@3$kSCNetworkConnectionPPPDisconnected@0$kSCNetworkConnectionPPPDisconnectingLink@10$kSCNetworkConnectionPPPHoldingLinkOff@11$kSCNetworkConnectionPPPInitializing@1$kSCNetworkConnectionPPPNegotiatingLink@4$kSCNetworkConnectionPPPNegotiatingNetwork@7$kSCNetworkConnectionPPPSuspended@12$kSCNetworkConnectionPPPTerminating@9$kSCNetworkConnectionPPPWaitingForCallBack@6$kSCNetworkConnectionPPPWaitingForRedial@13$kSCNetworkFlagsConnectionAutomatic@8$kSCNetworkFlagsConnectionRequired@4$kSCNetworkFlagsInterventionRequired@16$kSCNetworkFlagsIsDirect@131072$kSCNetworkFlagsIsLocalAddress@65536$kSCNetworkFlagsReachable@2$kSCNetworkFlagsTransientConnection@1$kSCNetworkReachabilityFlagsConnectionAutomatic@8$kSCNetworkReachabilityFlagsConnectionOnDemand@32$kSCNetworkReachabilityFlagsConnectionOnTraffic@8$kSCNetworkReachabilityFlagsConnectionRequired@4$kSCNetworkReachabilityFlagsInterventionRequired@16$kSCNetworkReachabilityFlagsIsDirect@131072$kSCNetworkReachabilityFlagsIsLocalAddress@65536$kSCNetworkReachabilityFlagsIsWWAN@262144$kSCNetworkReachabilityFlagsReachable@2$kSCNetworkReachabilityFlagsTransientConnection@1$kSCPreferencesNotificationApply@2$kSCPreferencesNotificationCommit@1$kSCStatusAccessError@1003$kSCStatusConnectionIgnore@5002$kSCStatusConnectionNoService@5001$kSCStatusFailed@1001$kSCStatusInvalidArgument@1002$kSCStatusKeyExists@1005$kSCStatusLocked@1006$kSCStatusMaxLink@3006$kSCStatusNeedLock@1007$kSCStatusNoConfigFile@3003$kSCStatusNoKey@1004$kSCStatusNoLink@3004$kSCStatusNoPrefsSession@3001$kSCStatusNoStoreServer@2002$kSCStatusNoStoreSession@2001$kSCStatusNotifierActive@2003$kSCStatusOK@0$kSCStatusPrefsBusy@3002$kSCStatusReachabilityUnknown@4001$kSCStatusStale@3005$"""
misc.update(
    {
        "SCPreferencesNotification": NewType("SCPreferencesNotification", int),
        "SCNetworkConnectionStatus": NewType("SCNetworkConnectionStatus", int),
        "SCNetworkReachabilityFlags": NewType("SCNetworkReachabilityFlags", int),
        "SCNetworkConnectionPPPStatus": NewType("SCNetworkConnectionPPPStatus", int),
    }
)
misc.update({})
misc.update(
    {
        "kSCNetworkConnectionSelectionOptionOnDemandHostName": "OnDemandHostName",
        "kSCNetworkConnectionPacketsOut": "PacketsOut",
        "kSCNetworkConnectionBytesIn": "BytesIn",
        "kSCNetworkConnectionPacketsIn": "PacketsIn",
        "kSCNetworkConnectionErrorsIn": "ErrorsIn",
        "kSCNetworkConnectionBytesOut": "BytesOut",
        "kSCNetworkConnectionSelectionOptionOnDemandRetry": "OnDemandRetry",
        "kSCNetworkConnectionErrorsOut": "ErrorsOut",
    }
)
functions = {
    "SCBondInterfaceCopyAll": (
        b"^{__CFArray=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCPreferencesGetTypeID": (b"Q",),
    "SCPreferencesSetValue": (b"Z^{__SCPreferences=}^{__CFString=}@",),
    "SCNetworkServiceEstablishDefaultConfiguration": (b"Z^{__SCNetworkService=}",),
    "SCNetworkServiceCreate": (
        b"^{__SCNetworkService=}^{__SCPreferences=}^{__SCNetworkInterface=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CNMarkPortalOnline": (b"Z^{__CFString=}",),
    "SCNetworkInterfaceCreateWithInterface": (
        b"^{__SCNetworkInterface=}^{__SCNetworkInterface=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreCopyDHCPInfo": (
        b"^{__CFDictionary=}^{__SCDynamicStore=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkSetCopyServices": (
        b"^{__CFArray=}^{__SCNetworkSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkSetSetCurrent": (b"Z^{__SCNetworkSet=}",),
    "SCDynamicStoreCopyComputerName": (
        b"^{__CFString=}^{__SCDynamicStore=}^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "SCNetworkConnectionScheduleWithRunLoop": (
        b"Z^{__SCNetworkConnection=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "SCDynamicStoreNotifyValue": (b"Z^{__SCDynamicStore=}^{__CFString=}",),
    "SCDynamicStoreCopyLocalHostName": (
        b"^{__CFString=}^{__SCDynamicStore=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceGetInterface": (
        b"^{__SCNetworkInterface=}^{__SCNetworkInterface=}",
    ),
    "SCNetworkSetRemoveService": (b"Z^{__SCNetworkSet=}^{__SCNetworkService=}",),
    "SCVLANInterfaceCopyAll": (
        b"^{__CFArray=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkProtocolGetConfiguration": (
        b"^{__CFDictionary=}^{__SCNetworkProtocol=}",
    ),
    "SCDynamicStoreCreateWithOptions": (
        b"^{__SCDynamicStore=}^{__CFAllocator=}^{__CFString=}^{__CFDictionary=}^?^{?=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__SCDynamicStore=}"},
                            1: {"type": b"^{__CFArray=}"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "SCVLANInterfaceCreate": (
        b"^{__SCNetworkInterface=}^{__SCPreferences=}^{__SCNetworkInterface=}^{__CFNumber=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkServiceGetInterface": (
        b"^{__SCNetworkInterface=}^{__SCNetworkService=}",
    ),
    "SCDynamicStoreKeyCreateProxies": (
        b"^{__CFString=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkReachabilityScheduleWithRunLoop": (
        b"Z^{__SCNetworkReachability=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "SCDynamicStoreAddTemporaryValue": (b"Z^{__SCDynamicStore=}^{__CFString=}@",),
    "SCNetworkConnectionStop": (b"Z^{__SCNetworkConnection=}Z",),
    "SCBondInterfaceCopyAvailableMemberInterfaces": (
        b"^{__CFArray=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkServiceAddProtocolType": (b"Z^{__SCNetworkService=}^{__CFString=}",),
    "SCDynamicStoreCreate": (
        b"^{__SCDynamicStore=}^{__CFAllocator=}^{__CFString=}^?^{?=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__SCDynamicStore=}"},
                            1: {"type": b"^{__CFArray=}"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "SCPreferencesPathCreateUniqueChild": (
        b"^{__CFString=}^{__SCPreferences=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkServiceGetServiceID": (b"^{__CFString=}^{__SCNetworkService=}",),
    "SCBondInterfaceCreate": (
        b"^{__SCNetworkInterface=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkConnectionCopyUserOptions": (
        b"^{__CFDictionary=}^{__SCNetworkConnection=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkReachabilitySetDispatchQueue": (b"Z^{__SCNetworkReachability=}@",),
    "SCPreferencesAddValue": (b"Z^{__SCPreferences=}^{__CFString=}@",),
    "SCDynamicStoreKeyCreateHostNames": (
        b"^{__CFString=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CNMarkPortalOffline": (b"Z^{__CFString=}",),
    "SCPreferencesCreate": (
        b"^{__SCPreferences=}^{__CFAllocator=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCVLANInterfaceSetOptions": (b"Z^{__SCNetworkInterface=}^{__CFDictionary=}",),
    "SCDynamicStoreSetMultiple": (
        b"Z^{__SCDynamicStore=}^{__CFDictionary=}^{__CFArray=}^{__CFArray=}",
    ),
    "SCNetworkConnectionCopyUserPreferences": (
        b"Z^{__CFDictionary=}^^{__CFString}^^{__CFDictionary}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"already_cfretained": True, "type_modifier": "o"},
                2: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "SCNetworkInterfaceGetBSDName": (b"^{__CFString=}^{__SCNetworkInterface=}",),
    "SCNetworkInterfaceSetExtendedConfiguration": (
        b"Z^{__SCNetworkInterface=}^{__CFString=}^{__CFDictionary=}",
    ),
    "SCNetworkCheckReachabilityByName": (
        b"Z^t^I",
        "",
        {
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                1: {"type_modifier": "o"},
            }
        },
    ),
    "SCNetworkInterfaceCopyMediaSubTypes": (
        b"^{__CFArray=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCErrorString": (b"^ti", "", {"retval": {"c_array_delimited_by_null": True}}),
    "DHCPClientPreferencesCopyApplicationOptions": (
        b"^C^{__CFString=}^i",
        "",
        {
            "retval": {
                "free_result": True,
                "already_cfretained": True,
                "c_array_length_in_arg": 1,
            },
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "SCNetworkProtocolGetProtocolType": (b"^{__CFString=}^{__SCNetworkProtocol=}",),
    "SCBondStatusGetTypeID": (b"Q",),
    "SCPreferencesCreateWithAuthorization": (
        b"^{__SCPreferences=}^{__CFAllocator=}^{__CFString=}^{__CFString=}^{AuthorizationOpaqueRef=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCBondInterfaceSetLocalizedDisplayName": (
        b"Z^{__SCNetworkInterface=}^{__CFString=}",
    ),
    "SCDynamicStoreKeyCreateConsoleUser": (
        b"^{__CFString=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkServiceSetName": (b"Z^{__SCNetworkService=}^{__CFString=}",),
    "SCDynamicStoreSetNotificationKeys": (
        b"Z^{__SCDynamicStore=}^{__CFArray=}^{__CFArray=}",
    ),
    "SCDynamicStoreKeyCreateNetworkInterface": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceSetMediaOptions": (
        b"Z^{__SCNetworkInterface=}^{__CFString=}^{__CFArray=}",
    ),
    "SCPreferencesGetValue": (b"@^{__SCPreferences=}^{__CFString=}",),
    "SCNetworkInterfaceGetConfiguration": (
        b"^{__CFDictionary=}^{__SCNetworkInterface=}",
    ),
    "DHCPInfoGetLeaseStartTime": (b"^{__CFDate=}^{__CFDictionary=}",),
    "SCNetworkInterfaceGetExtendedConfiguration": (
        b"^{__CFDictionary=}^{__SCNetworkInterface=}^{__CFString=}",
    ),
    "SCVLANInterfaceGetPhysicalInterface": (
        b"^{__SCNetworkInterface=}^{__SCNetworkInterface=}",
    ),
    "SCNetworkConnectionCopyServiceID": (
        b"^{__CFString=}^{__SCNetworkConnection=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreGetTypeID": (b"Q",),
    "SCNetworkSetCopy": (
        b"^{__SCNetworkSet=}^{__SCPreferences=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCBondStatusGetMemberInterfaces": (b"^{__CFArray=}^{__SCBondStatus=}",),
    "SCNetworkProtocolSetEnabled": (b"Z^{__SCNetworkProtocol=}Z",),
    "SCNetworkConnectionUnscheduleFromRunLoop": (
        b"Z^{__SCNetworkConnection=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "SCPreferencesSynchronize": (b"v^{__SCPreferences=}",),
    "SCPreferencesSetComputerName": (b"Z^{__SCPreferences=}^{__CFString=}I",),
    "SCVLANInterfaceRemove": (b"Z^{__SCNetworkInterface=}",),
    "SCDynamicStoreKeyCreateNetworkInterfaceEntity": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreCopyConsoleUser": (
        b"^{__CFString=}^{__SCDynamicStore=}^I^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}},
        },
    ),
    "SCVLANInterfaceCopyAvailablePhysicalInterfaces": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkConnectionCreateWithServiceID": (
        b"^{__SCNetworkConnection=}^{__CFAllocator=}^{__CFString=}^?^{?=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__SCNetworkConnection=}"},
                            1: {"type": b"i"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "SCNetworkServiceGetTypeID": (b"Q",),
    "SCCopyLastError": (b"^{__CFError=}", "", {"retval": {"already_cfretained": True}}),
    "SCNetworkServiceRemoveProtocolType": (b"Z^{__SCNetworkService=}^{__CFString=}",),
    "SCDynamicStoreKeyCreateComputerName": (
        b"^{__CFString=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCPreferencesUnscheduleFromRunLoop": (
        b"Z^{__SCPreferences=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "SCNetworkReachabilityCreateWithName": (
        b"^{__SCNetworkReachability=}^{__CFAllocator=}^t",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "SCBondInterfaceRemove": (b"Z^{__SCNetworkInterface=}",),
    "SCNetworkCheckReachabilityByAddress": (
        b"Z^{sockaddr=CC[14c]}I^I",
        "",
        {"arguments": {0: {"type_modifier": "n"}, 2: {"type_modifier": "o"}}},
    ),
    "SCNetworkSetContainsInterface": (b"Z^{__SCNetworkSet=}^{__SCNetworkInterface=}",),
    "SCNetworkServiceRemove": (b"Z^{__SCNetworkService=}",),
    "SCPreferencesApplyChanges": (b"Z^{__SCPreferences=}",),
    "CNCopySupportedInterfaces": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCBondInterfaceGetMemberInterfaces": (b"^{__CFArray=}^{__SCNetworkInterface=}",),
    "SCPreferencesPathGetLink": (b"^{__CFString=}^{__SCPreferences=}^{__CFString=}",),
    "SCPreferencesPathSetLink": (b"Z^{__SCPreferences=}^{__CFString=}^{__CFString=}",),
    "SCNetworkInterfaceGetHardwareAddressString": (
        b"^{__CFString=}^{__SCNetworkInterface=}",
    ),
    "SCNetworkSetGetTypeID": (b"Q",),
    "SCNetworkReachabilityUnscheduleFromRunLoop": (
        b"Z^{__SCNetworkReachability=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "SCNetworkServiceSetEnabled": (b"Z^{__SCNetworkService=}Z",),
    "SCPreferencesSetDispatchQueue": (b"Z^{__SCPreferences=}@",),
    "SCNetworkReachabilityGetTypeID": (b"Q",),
    "SCDynamicStoreAddValue": (b"Z^{__SCDynamicStore=}^{__CFString=}@",),
    "SCNetworkServiceGetEnabled": (b"Z^{__SCNetworkService=}",),
    "SCNetworkSetAddService": (b"Z^{__SCNetworkSet=}^{__SCNetworkService=}",),
    "SCNetworkConnectionGetTypeID": (b"Q",),
    "SCDynamicStoreCopyValue": (
        b"@^{__SCDynamicStore=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCBondInterfaceSetMemberInterfaces": (b"Z^{__SCNetworkInterface=}^{__CFArray=}",),
    "DHCPInfoGetOptionData": (b"^{__CFData=}^{__CFDictionary=}C",),
    "SCDynamicStoreCopyMultiple": (
        b"^{__CFDictionary=}^{__SCDynamicStore=}^{__CFArray=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCBondStatusGetInterfaceStatus": (
        b"^{__CFDictionary=}^{__SCBondStatus=}^{__SCNetworkInterface=}",
    ),
    "SCNetworkConnectionCopyExtendedStatus": (
        b"^{__CFDictionary=}^{__SCNetworkConnection=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkProtocolGetTypeID": (b"Q",),
    "SCNetworkSetGetName": (b"^{__CFString=}^{__SCNetworkSet=}",),
    "SCPreferencesSetCallback": (
        b"Z^{__SCPreferences=}^?^{?=q^v^?^?^?}",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__SCPreferences=}"},
                            1: {"type": b"I"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "SCNetworkInterfaceGetSupportedProtocolTypes": (
        b"^{__CFArray=}^{__SCNetworkInterface=}",
    ),
    "SCNetworkServiceCopyProtocol": (
        b"^{__SCNetworkProtocol=}^{__SCNetworkService=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreRemoveValue": (b"Z^{__SCDynamicStore=}^{__CFString=}",),
    "SCVLANInterfaceGetTag": (b"^{__CFNumber=}^{__SCNetworkInterface=}",),
    "SCNetworkServiceCopy": (
        b"^{__SCNetworkService=}^{__SCPreferences=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreCopyKeyList": (
        b"^{__CFArray=}^{__SCDynamicStore=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceGetLocalizedDisplayName": (
        b"^{__CFString=}^{__SCNetworkInterface=}",
    ),
    "SCDynamicStoreCopyNotifiedKeys": (
        b"^{__CFArray=}^{__SCDynamicStore=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceCopyMediaSubTypeOptions": (
        b"^{__CFArray=}^{__CFArray=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "DHCPInfoGetLeaseExpirationTime": (b"^{__CFDate=}^{__CFDictionary=}",),
    "SCVLANInterfaceSetLocalizedDisplayName": (
        b"Z^{__SCNetworkInterface=}^{__CFString=}",
    ),
    "SCVLANInterfaceSetPhysicalInterfaceAndTag": (
        b"Z^{__SCNetworkInterface=}^{__SCNetworkInterface=}^{__CFNumber=}",
    ),
    "SCNetworkProtocolSetConfiguration": (
        b"Z^{__SCNetworkProtocol=}^{__CFDictionary=}",
    ),
    "SCBondInterfaceGetOptions": (b"^{__CFDictionary=}^{__SCNetworkInterface=}",),
    "SCBondInterfaceCopyStatus": (
        b"^{__SCBondStatus=}^{__SCNetworkInterface=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceGetTypeID": (b"Q",),
    "CNCopyCurrentNetworkInfo": (
        b"^{__CFDictionary=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CNSetSupportedSSIDs": (b"Z^{__CFArray=}",),
    "SCNetworkConnectionCopyStatistics": (
        b"^{__CFDictionary=}^{__SCNetworkConnection=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreKeyCreate": (
        b"^{__CFString=}^{__CFAllocator=}@",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"printf_format": True}},
            "variadic": True,
        },
    ),
    "SCPreferencesCommitChanges": (b"Z^{__SCPreferences=}",),
    "SCNetworkSetSetServiceOrder": (b"Z^{__SCNetworkSet=}^{__CFArray=}",),
    "SCPreferencesLock": (b"Z^{__SCPreferences=}Z",),
    "SCNetworkSetCopyAll": (
        b"^{__CFArray=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceRefreshConfiguration": (b"Z^{__CFString=}",),
    "SCNetworkSetSetName": (b"Z^{__SCNetworkSet=}^{__CFString=}",),
    "SCPreferencesPathRemoveValue": (b"Z^{__SCPreferences=}^{__CFString=}",),
    "SCDynamicStoreCopyProxies": (
        b"^{__CFDictionary=}^{__SCDynamicStore=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkServiceGetName": (b"^{__CFString=}^{__SCNetworkService=}",),
    "SCNetworkServiceCopyAll": (
        b"^{__CFArray=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceSetMTU": (b"Z^{__SCNetworkInterface=}i",),
    "DHCPClientPreferencesSetApplicationOptions": (
        b"Z^{__CFString=}^Cq",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "SCDynamicStoreKeyCreateNetworkGlobalEntity": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkSetCopyCurrent": (
        b"^{__SCNetworkSet=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkInterfaceCopyAll": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCPreferencesRemoveValue": (b"Z^{__SCPreferences=}^{__CFString=}",),
    "SCPreferencesPathGetValue": (
        b"^{__CFDictionary=}^{__SCPreferences=}^{__CFString=}",
    ),
    "SCDynamicStoreSetValue": (b"Z^{__SCDynamicStore=}^{__CFString=}@",),
    "SCNetworkConnectionGetStatus": (b"i^{__SCNetworkConnection=}",),
    "SCNetworkServiceCopyProtocols": (
        b"^{__CFArray=}^{__SCNetworkService=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreCopyLocation": (
        b"^{__CFString=}^{__SCDynamicStore=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkReachabilityCreateWithAddress": (
        b"^{__SCNetworkReachability=}^{__CFAllocator=}^{sockaddr=CC[14c]}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "SCNetworkSetRemove": (b"Z^{__SCNetworkSet=}",),
    "SCNetworkInterfaceSetConfiguration": (
        b"Z^{__SCNetworkInterface=}^{__CFDictionary=}",
    ),
    "SCPreferencesSetLocalHostName": (b"Z^{__SCPreferences=}^{__CFString=}",),
    "SCNetworkInterfaceGetInterfaceType": (b"^{__CFString=}^{__SCNetworkInterface=}",),
    "SCPreferencesGetSignature": (b"^{__CFData=}^{__SCPreferences=}",),
    "SCNetworkConnectionStart": (b"Z^{__SCNetworkConnection=}^{__CFDictionary=}Z",),
    "SCNetworkInterfaceCopyMTU": (
        b"Z^{__SCNetworkInterface=}^i^i^i",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
            },
        },
    ),
    "SCNetworkSetGetServiceOrder": (b"^{__CFArray=}^{__SCNetworkSet=}",),
    "SCDynamicStoreKeyCreateLocation": (
        b"^{__CFString=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCBondInterfaceSetOptions": (b"Z^{__SCNetworkInterface=}^{__CFDictionary=}",),
    "SCDynamicStoreCreateRunLoopSource": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}^{__SCDynamicStore=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkProtocolGetEnabled": (b"Z^{__SCNetworkProtocol=}",),
    "SCNetworkSetCreate": (
        b"^{__SCNetworkSet=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkConnectionSetDispatchQueue": (b"Z^{__SCNetworkConnection=}@",),
    "SCDynamicStoreKeyCreateNetworkServiceEntity": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCDynamicStoreSetDispatchQueue": (b"Z^{__SCDynamicStore=}@",),
    "SCNetworkReachabilitySetCallback": (
        b"Z^{__SCNetworkReachability=}^?^{?=q^v^?^?^?}",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__SCNetworkReachability=}"},
                            1: {"type": b"I"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "SCNetworkInterfaceGetSupportedInterfaceTypes": (
        b"^{__CFArray=}^{__SCNetworkInterface=}",
    ),
    "SCPreferencesCopyKeyList": (
        b"^{__CFArray=}^{__SCPreferences=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "SCNetworkReachabilityCreateWithAddressPair": (
        b"^{__SCNetworkReachability=}^{__CFAllocator=}^{sockaddr=CC[14c]}^{sockaddr=CC[14c]}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}, 2: {"type_modifier": "n"}},
        },
    ),
    "SCNetworkInterfaceForceConfigurationRefresh": (b"Z^{__SCNetworkInterface=}",),
    "SCNetworkSetGetSetID": (b"^{__CFString=}^{__SCNetworkSet=}",),
    "SCNetworkInterfaceCopyMediaOptions": (
        b"Z^{__SCNetworkInterface=}^^{__CFDictionary}^^{__CFDictionary}^^{__CFArray}Z",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
            },
        },
    ),
    "SCPreferencesScheduleWithRunLoop": (
        b"Z^{__SCPreferences=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "SCVLANInterfaceGetOptions": (b"^{__CFDictionary=}^{__SCNetworkInterface=}",),
    "SCNetworkReachabilityGetFlags": (
        b"Z^{__SCNetworkReachability=}^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "SCPreferencesPathSetValue": (
        b"Z^{__SCPreferences=}^{__CFString=}^{__CFDictionary=}",
    ),
    "SCPreferencesUnlock": (b"Z^{__SCPreferences=}",),
    "SCError": (b"i",),
}
aliases = {
    "kSCPropNetDNSSearchOrder": "kSCPropNetDNSSearchOrder",
    "kSCPropNetPPPAuthEAPPlugins": "kSCPropNetPPPAuthEAPPlugins",
    "kSCPropNet6to4Relay": "kSCPropNet6to4Relay",
    "kSCPropUserDefinedName": "kSCPropUserDefinedName",
    "kSCPropNetProxiesSOCKSProxy": "kSCPropNetProxiesSOCKSProxy",
    "kSCValNetIPSecSharedSecretEncryptionKeychain": "kSCValNetIPSecSharedSecretEncryptionKeychain",
    "kSCPropNetProxiesRTSPUser": "kSCPropNetProxiesRTSPUser",
    "kSCPropNetPPPDisconnectOnFastUserSwitch": "kSCPropNetPPPDisconnectOnFastUserSwitch",
    "kSCPropNetEthernetMediaSubType": "kSCPropNetEthernetMediaSubType",
    "kSCPropNetPPPCommDisplayTerminalWindow": "kSCPropNetPPPCommDisplayTerminalWindow",
    "kSCPropNetModemConnectionScript": "kSCPropNetModemConnectionScript",
    "kSCValNetPPPAuthPromptAfter": "kSCValNetPPPAuthPromptAfter",
    "kSCPropNetIPSecRemoteAddress": "kSCPropNetIPSecRemoteAddress",
    "kSCPropNetProxiesGopherEnable": "kSCPropNetProxiesGopherEnable",
    "kSCPropNetIPv4DHCPClientID": "kSCPropNetIPv4DHCPClientID",
    "kSCPrefSets": "kSCPrefSets",
    "kSCValNetIPSecLocalIdentifierTypeKeyID": "kSCValNetIPSecLocalIdentifierTypeKeyID",
    "kSCValNetIPSecAuthenticationMethodHybrid": "kSCValNetIPSecAuthenticationMethodHybrid",
    "kSCPropNetModemDeviceContextID": "kSCPropNetModemDeviceContextID",
    "kSCValNetIPv6ConfigMethodManual": "kSCValNetIPv6ConfigMethodManual",
    "kSCPropNetPPPIdleReminderTimer": "kSCPropNetPPPIdleReminderTimer",
    "kSCPropNetModemAccessPointName": "kSCPropNetModemAccessPointName",
    "kSCPropNetModemConnectSpeed": "kSCPropNetModemConnectSpeed",
    "kSCPropNetDNSSupplementalMatchDomains": "kSCPropNetDNSSupplementalMatchDomains",
    "kSCPropNetPPPLCPEchoEnabled": "kSCPropNetPPPLCPEchoEnabled",
    "kSCPropNetProxiesExceptionsList": "kSCPropNetProxiesExceptionsList",
    "kSCPropNetProxiesGopherProxy": "kSCPropNetProxiesGopherProxy",
    "kSCDynamicStoreDomainPlugin": "kSCDynamicStoreDomainPlugin",
    "kSCResvLink": "kSCResvLink",
    "kSCPropNetPPPOverridePrimary": "kSCPropNetPPPOverridePrimary",
    "kSCValNetIPSecXAuthPasswordEncryptionKeychain": "kSCValNetIPSecXAuthPasswordEncryptionKeychain",
    "kSCValNetL2TPTransportIP": "kSCValNetL2TPTransportIP",
    "kSCPropNetIPSecLocalCertificate": "kSCPropNetIPSecLocalCertificate",
    "kSCEntUsersConsoleUser": "kSCEntUsersConsoleUser",
    "kSCPropNetIPv4ConfigMethod": "kSCPropNetIPv4ConfigMethod",
    "kSCPropNetProxiesProxyAutoConfigURLString": "kSCPropNetProxiesProxyAutoConfigURLString",
    "kSCPrefCurrentSet": "kSCPrefCurrentSet",
    "kSCPropNetPPPDialOnDemand": "kSCPropNetPPPDialOnDemand",
    "kSCPropNetIPv6ConfigMethod": "kSCPropNetIPv6ConfigMethod",
    "kSCValNetIPv6ConfigMethod6to4": "kSCValNetIPv6ConfigMethod6to4",
    "kSCPropNetPPPLCPCompressionPField": "kSCPropNetPPPLCPCompressionPField",
    "kSCPropNetPPPRetryConnectTime": "kSCPropNetPPPRetryConnectTime",
    "kSCPropNetProxiesGopherPort": "kSCPropNetProxiesGopherPort",
    "kSCEntNetDHCP": "kSCEntNetDHCP",
    "kSCEntNetInterface": "kSCEntNetInterface",
    "kSCPropNetProxiesHTTPSEnable": "kSCPropNetProxiesHTTPSEnable",
    "kSCPropNetInterfaceHardware": "kSCPropNetInterfaceHardware",
    "kSCPropNetModemNote": "kSCPropNetModemNote",
    "kSCPropNetAirPortAllowNetCreation": "kSCPropNetAirPortAllowNetCreation",
    "kSCDynamicStorePropNetServiceIDs": "kSCDynamicStorePropNetServiceIDs",
    "kSCResvInactive": "kSCResvInactive",
    "kSCPropNetPPPLCPMTU": "kSCPropNetPPPLCPMTU",
    "kSCPropNetEthernetMediaOptions": "kSCPropNetEthernetMediaOptions",
    "kSCPropNetProxiesHTTPUser": "kSCPropNetProxiesHTTPUser",
    "kSCPropNetInterfaceSupportsModemOnHold": "kSCPropNetInterfaceSupportsModemOnHold",
    "kSCPropNetProxiesHTTPSUser": "kSCPropNetProxiesHTTPSUser",
    "kSCPropUsersConsoleUserUID": "kSCPropUsersConsoleUserUID",
    "kSCPropNetL2TPIPSecSharedSecret": "kSCPropNetL2TPIPSecSharedSecret",
    "kSCValNetIPv4ConfigMethodLinkLocal": "kSCValNetIPv4ConfigMethodLinkLocal",
    "kSCPropNetProxiesHTTPEnable": "kSCPropNetProxiesHTTPEnable",
    "kSCPropNetModemDeviceVendor": "kSCPropNetModemDeviceVendor",
    "kSCPropNetIPv4SubnetMasks": "kSCPropNetIPv4SubnetMasks",
    "kSCPropNetProxiesHTTPSPort": "kSCPropNetProxiesHTTPSPort",
    "kSCPropNetPPPIdleReminder": "kSCPropNetPPPIdleReminder",
    "kSCValNetAirPortJoinModeAutomatic": "kSCValNetAirPortJoinModeAutomatic",
    "kSCDynamicStorePropNetPrimaryInterface": "kSCDynamicStorePropNetPrimaryInterface",
    "kSCEntNetEthernet": "kSCEntNetEthernet",
    "kSCPropNetIPv6Addresses": "kSCPropNetIPv6Addresses",
    "kSCDynamicStoreDomainFile": "kSCDynamicStoreDomainFile",
    "kSCPropNetPPPDisconnectOnLogout": "kSCPropNetPPPDisconnectOnLogout",
    "kSCDynamicStorePropNetPrimaryService": "kSCDynamicStorePropNetPrimaryService",
    "kSCPropNetPPPCommRedialInterval": "kSCPropNetPPPCommRedialInterval",
    "kSCValNetIPSecAuthenticationMethodSharedSecret": "kSCValNetIPSecAuthenticationMethodSharedSecret",
    "kSCPropNetPPPAuthProtocol": "kSCPropNetPPPAuthProtocol",
    "kSCValNetIPv4ConfigMethodBOOTP": "kSCValNetIPv4ConfigMethodBOOTP",
    "kSCDynamicStoreDomainState": "kSCDynamicStoreDomainState",
    "kSCPropUsersConsoleUserGID": "kSCPropUsersConsoleUserGID",
    "kSCPropNetInterfaceDeviceName": "kSCPropNetInterfaceDeviceName",
    "kSCPropNetModemHoldEnabled": "kSCPropNetModemHoldEnabled",
    "kSCEntNetFireWire": "kSCEntNetFireWire",
    "kSCPrefNetworkServices": "kSCPrefNetworkServices",
    "kSCValNetInterfaceTypeFireWire": "kSCValNetInterfaceTypeFireWire",
    "kSCPropNetModemDialMode": "kSCPropNetModemDialMode",
    "kSCPropNetProxiesGopherUser": "kSCPropNetProxiesGopherUser",
    "kSCValNetInterfaceTypePPP": "kSCValNetInterfaceTypePPP",
    "kSCPropNetPPPLogfile": "kSCPropNetPPPLogfile",
    "kSCValNetPPPAuthPromptBefore": "kSCValNetPPPAuthPromptBefore",
    "kSCPropNetModemSpeed": "kSCPropNetModemSpeed",
    "kSCPropNetLinkActive": "kSCPropNetLinkActive",
    "kSCValNetIPSecAuthenticationMethodCertificate": "kSCValNetIPSecAuthenticationMethodCertificate",
    "kSCPropNetModemDeviceModel": "kSCPropNetModemDeviceModel",
    "kSCPropNetIPv6Router": "kSCPropNetIPv6Router",
    "kSCPropNetIPSecConnectTime": "kSCPropNetIPSecConnectTime",
    "kSCPropNetEthernetMTU": "kSCPropNetEthernetMTU",
    "kSCPropNetProxiesRTSPProxy": "kSCPropNetProxiesRTSPProxy",
    "kSCPropNetIPSecXAuthName": "kSCPropNetIPSecXAuthName",
    "kSCPropNetL2TPTransport": "kSCPropNetL2TPTransport",
    "kSCValNetIPv6ConfigMethodRouterAdvertisement": "kSCValNetIPv6ConfigMethodRouterAdvertisement",
    "kSCPropNetDNSServerPort": "kSCPropNetDNSServerPort",
    "kSCPropNetDNSSupplementalMatchOrders": "kSCPropNetDNSSupplementalMatchOrders",
    "kSCPropNetSMBWorkgroup": "kSCPropNetSMBWorkgroup",
    "kSCPropSystemComputerName": "kSCPropSystemComputerName",
    "kSCValNetInterfaceSubTypePPPoE": "kSCValNetInterfaceSubTypePPPoE",
    "kSCPropNetPPPAuthName": "kSCPropNetPPPAuthName",
    "kSCDynamicStoreDomainSetup": "kSCDynamicStoreDomainSetup",
    "kSCPropNetProxiesFTPProxy": "kSCPropNetProxiesFTPProxy",
    "kSCPropNetPPPCCPMPPE40Enabled": "kSCPropNetPPPCCPMPPE40Enabled",
    "kSCPropNetModemPulseDial": "kSCPropNetModemPulseDial",
    "kSCPropNetLinkDetaching": "kSCPropNetLinkDetaching",
    "kSCPropUsersConsoleUserName": "kSCPropUsersConsoleUserName",
    "kSCPropNetIPv6DestAddresses": "kSCPropNetIPv6DestAddresses",
    "kSCPropVersion": "kSCPropVersion",
    "kSCCompNetwork": "kSCCompNetwork",
    "kSCPropNetIPv6Flags": "kSCPropNetIPv6Flags",
    "kSCPrefSystem": "kSCPrefSystem",
    "kSCValNetIPv4ConfigMethodManual": "kSCValNetIPv4ConfigMethodManual",
    "kSCValNetInterfaceSubTypePPPSerial": "kSCValNetInterfaceSubTypePPPSerial",
    "kSCPropNetModemHoldDisconnectOnAnswer": "kSCPropNetModemHoldDisconnectOnAnswer",
    "kSCEntNetPPP": "kSCEntNetPPP",
    "kSCPropNetPPPLastCause": "kSCPropNetPPPLastCause",
    "kSCPropNetAirPortAuthPasswordEncryption": "kSCPropNetAirPortAuthPasswordEncryption",
    "kSCPropNetPPPCommConnectDelay": "kSCPropNetPPPCommConnectDelay",
    "kSCPropNetSMBNetBIOSName": "kSCPropNetSMBNetBIOSName",
    "kSCPropNetProxiesHTTPSProxy": "kSCPropNetProxiesHTTPSProxy",
    "kSCPropNetProxiesProxyAutoConfigEnable": "kSCPropNetProxiesProxyAutoConfigEnable",
    "kSCValNetInterfaceTypeIPSec": "kSCValNetInterfaceTypeIPSec",
    "kSCPropNetIPSecLocalIdentifierType": "kSCPropNetIPSecLocalIdentifierType",
    "kSCValNetInterfaceTypeEthernet": "kSCValNetInterfaceTypeEthernet",
    "kSCValNetPPPAuthProtocolCHAP": "kSCValNetPPPAuthProtocolCHAP",
    "kSCPropNetModemHoldCallWaitingAudibleAlert": "kSCPropNetModemHoldCallWaitingAudibleAlert",
    "kSCPropNetPPPAuthPasswordEncryption": "kSCPropNetPPPAuthPasswordEncryption",
    "kSCValNetInterfaceSubTypeL2TP": "kSCValNetInterfaceSubTypeL2TP",
    "kSCDynamicStorePropNetInterfaces": "kSCDynamicStorePropNetInterfaces",
    "kSCPropNetIPv6PrefixLength": "kSCPropNetIPv6PrefixLength",
    "kSCValNetInterfaceSubTypePPTP": "kSCValNetInterfaceSubTypePPTP",
    "kSCPropNetPPPCommAlternateRemoteAddress": "kSCPropNetPPPCommAlternateRemoteAddress",
    "kSCPropNetIPSecXAuthEnabled": "kSCPropNetIPSecXAuthEnabled",
    "kSCPropNetSMBWINSAddresses": "kSCPropNetSMBWINSAddresses",
    "kSCPropNetPPPPlugins": "kSCPropNetPPPPlugins",
    "kSCDynamicStorePropSetupCurrentSet": "kSCDynamicStorePropSetupCurrentSet",
    "kSCPropNetInterfaceType": "kSCPropNetInterfaceType",
    "kSCPropNetModemSpeaker": "kSCPropNetModemSpeaker",
    "kSCPropNetPPPCCPEnabled": "kSCPropNetPPPCCPEnabled",
    "kSCPropNetPPPDisconnectOnSleep": "kSCPropNetPPPDisconnectOnSleep",
    "kSCPropNetProxiesExcludeSimpleHostnames": "kSCPropNetProxiesExcludeSimpleHostnames",
    "kSCEntNetLink": "kSCEntNetLink",
    "kSCPropNetPPPLCPEchoFailure": "kSCPropNetPPPLCPEchoFailure",
    "kSCPropNetAirPortAuthPassword": "kSCPropNetAirPortAuthPassword",
    "kSCPropNetIPv4BroadcastAddresses": "kSCPropNetIPv4BroadcastAddresses",
    "kSCValNetSMBNetBIOSNodeTypeBroadcast": "kSCValNetSMBNetBIOSNodeTypeBroadcast",
    "kSCEntNetModem": "kSCEntNetModem",
    "kSCPropNetIPSecStatus": "kSCPropNetIPSecStatus",
    "kSCPropNetPPPCommRedialCount": "kSCPropNetPPPCommRedialCount",
    "kSCPropNetSMBNetBIOSNodeType": "kSCPropNetSMBNetBIOSNodeType",
    "kSCValNetIPSecXAuthPasswordEncryptionPrompt": "kSCValNetIPSecXAuthPasswordEncryptionPrompt",
    "kSCCompGlobal": "kSCCompGlobal",
    "kSCPropNetProxiesHTTPPort": "kSCPropNetProxiesHTTPPort",
    "kSCPropSystemComputerNameEncoding": "kSCPropSystemComputerNameEncoding",
    "kSCPropNetModemErrorCorrection": "kSCPropNetModemErrorCorrection",
    "kSCPropNetProxiesSOCKSEnable": "kSCPropNetProxiesSOCKSEnable",
    "kSCValNetSMBNetBIOSNodeTypeHybrid": "kSCValNetSMBNetBIOSNodeTypeHybrid",
    "kSCPropNetPPPDisconnectOnIdle": "kSCPropNetPPPDisconnectOnIdle",
    "kSCValNetAirPortJoinModeStrongest": "kSCValNetAirPortJoinModeStrongest",
    "kSCPropNetPPPDisconnectOnIdleTimer": "kSCPropNetPPPDisconnectOnIdleTimer",
    "kSCPropNetIPSecSharedSecretEncryption": "kSCPropNetIPSecSharedSecretEncryption",
    "kSCEntNetAirPort": "kSCEntNetAirPort",
    "kSCEntNetProxies": "kSCEntNetProxies",
    "kSCValNetPPPAuthProtocolEAP": "kSCValNetPPPAuthProtocolEAP",
    "kSCValNetIPv4ConfigMethodPPP": "kSCValNetIPv4ConfigMethodPPP",
    "kSCPropNetPPPSessionTimer": "kSCPropNetPPPSessionTimer",
    "kSCValNetIPv6ConfigMethodAutomatic": "kSCValNetIPv6ConfigMethodAutomatic",
    "kSCPropNetPPPLCPReceiveACCM": "kSCPropNetPPPLCPReceiveACCM",
    "kSCValNetModemDialModeWaitForDialTone": "kSCValNetModemDialModeWaitForDialTone",
    "kSCValNetL2TPTransportIPSec": "kSCValNetL2TPTransportIPSec",
    "kSCPropNetProxiesProxyAutoDiscoveryEnable": "kSCPropNetProxiesProxyAutoDiscoveryEnable",
    "kSCPropNetProxiesRTSPPort": "kSCPropNetProxiesRTSPPort",
    "kSCPropNetDNSServerTimeout": "kSCPropNetDNSServerTimeout",
    "kSCNetworkReachabilityFlagsConnectionAutomatic": "kSCNetworkReachabilityFlagsConnectionOnTraffic",
    "kSCPropNetPPPLCPEchoInterval": "kSCPropNetPPPLCPEchoInterval",
    "SCVLANInterfaceRef": "SCNetworkInterfaceRef",
    "kSCValNetInterfaceType6to4": "kSCValNetInterfaceType6to4",
    "kSCPropNetPPPCommRedialEnabled": "kSCPropNetPPPCommRedialEnabled",
    "kSCValNetL2TPIPSecSharedSecretEncryptionKeychain": "kSCValNetL2TPIPSecSharedSecretEncryptionKeychain",
    "kSCEntNetL2TP": "kSCEntNetL2TP",
    "kSCPropNetPPPDisconnectTime": "kSCPropNetPPPDisconnectTime",
    "kSCEntNetSMB": "kSCEntNetSMB",
    "kSCPropNetPPPConnectTime": "kSCPropNetPPPConnectTime",
    "kSCValNetModemDialModeIgnoreDialTone": "kSCValNetModemDialModeIgnoreDialTone",
    "kSCPropNetDNSOptions": "kSCPropNetDNSOptions",
    "kSCPropNetProxiesFTPPort": "kSCPropNetProxiesFTPPort",
    "kSCCompInterface": "kSCCompInterface",
    "kSCValNetIPv4ConfigMethodINFORM": "kSCValNetIPv4ConfigMethodINFORM",
    "kSCCompService": "kSCCompService",
    "kSCEntNetPPPoE": "kSCEntNetPPPoE",
    "kSCPropNetIPv4Addresses": "kSCPropNetIPv4Addresses",
    "kSCPropNetPPPLCPCompressionACField": "kSCPropNetPPPLCPCompressionACField",
    "kSCPropNetIPSecLocalIdentifier": "kSCPropNetIPSecLocalIdentifier",
    "kSCPropNetProxiesFTPEnable": "kSCPropNetProxiesFTPEnable",
    "kSCPropNetDNSDomainName": "kSCPropNetDNSDomainName",
    "kSCValNetSMBNetBIOSNodeTypeMixed": "kSCValNetSMBNetBIOSNodeTypeMixed",
    "kSCEntNet6to4": "kSCEntNet6to4",
    "kSCValNetPPPAuthProtocolPAP": "kSCValNetPPPAuthProtocolPAP",
    "kSCValNetPPPAuthPasswordEncryptionKeychain": "kSCValNetPPPAuthPasswordEncryptionKeychain",
    "kSCValNetAirPortJoinModeRecent": "kSCValNetAirPortJoinModeRecent",
    "kSCPropNetLocalHostName": "kSCPropNetLocalHostName",
    "kSCValNetAirPortJoinModePreferred": "kSCValNetAirPortJoinModePreferred",
    "kSCDynamicStorePropSetupLastUpdated": "kSCDynamicStorePropSetupLastUpdated",
    "kSCPropNetIPv4Router": "kSCPropNetIPv4Router",
    "SCBondInterfaceRef": "SCNetworkInterfaceRef",
    "kSCPropNetIPSecXAuthPassword": "kSCPropNetIPSecXAuthPassword",
    "kSCPropNetDNSSearchDomains": "kSCPropNetDNSSearchDomains",
    "kSCPropNetAirPortPreferredNetwork": "kSCPropNetAirPortPreferredNetwork",
    "kSCPropNetDNSServerAddresses": "kSCPropNetDNSServerAddresses",
    "kSCPropNetL2TPIPSecSharedSecretEncryption": "kSCPropNetL2TPIPSecSharedSecretEncryption",
    "kSCPropNetIPv4DestAddresses": "kSCPropNetIPv4DestAddresses",
    "kSCEntNetDNS": "kSCEntNetDNS",
    "kSCPropNetProxiesProxyAutoConfigJavaScript": "kSCPropNetProxiesProxyAutoConfigJavaScript",
    "kSCPropNetPPPIPCPCompressionVJ": "kSCPropNetPPPIPCPCompressionVJ",
    "kSCCompHostNames": "kSCCompHostNames",
    "kSCPropNetAirPortSavePasswords": "kSCPropNetAirPortSavePasswords",
    "kSCCompAnyRegex": "kSCCompAnyRegex",
    "kSCValNetAirPortAuthPasswordEncryptionKeychain": "kSCValNetAirPortAuthPasswordEncryptionKeychain",
    "kSCCompSystem": "kSCCompSystem",
    "kSCPropNetIPSecAuthenticationMethod": "kSCPropNetIPSecAuthenticationMethod",
    "kSCCompUsers": "kSCCompUsers",
    "kSCPropNetPPPVerboseLogging": "kSCPropNetPPPVerboseLogging",
    "kSCDynamicStoreDomainPrefs": "kSCDynamicStoreDomainPrefs",
    "kSCValNetPPPAuthProtocolMSCHAP2": "kSCValNetPPPAuthProtocolMSCHAP2",
    "kSCPropNetAirPortPowerEnabled": "kSCPropNetAirPortPowerEnabled",
    "kSCPropNetInterfaceSubType": "kSCPropNetInterfaceSubType",
    "kSCPropNetIPSecXAuthPasswordEncryption": "kSCPropNetIPSecXAuthPasswordEncryption",
    "kSCValNetSMBNetBIOSNodeTypePeer": "kSCValNetSMBNetBIOSNodeTypePeer",
    "kSCPropNetProxiesSOCKSPort": "kSCPropNetProxiesSOCKSPort",
    "kSCValNetPPPAuthProtocolMSCHAP1": "kSCValNetPPPAuthProtocolMSCHAP1",
    "kSCPropNetSMBNetBIOSScope": "kSCPropNetSMBNetBIOSScope",
    "kSCPropNetProxiesSOCKSUser": "kSCPropNetProxiesSOCKSUser",
    "kSCEntNetPPPSerial": "kSCEntNetPPPSerial",
    "kSCPropNetPPPCCPMPPE128Enabled": "kSCPropNetPPPCCPMPPE128Enabled",
    "kSCValNetModemDialModeManual": "kSCValNetModemDialModeManual",
    "kSCPropNetPPPLCPTransmitACCM": "kSCPropNetPPPLCPTransmitACCM",
    "kSCPropMACAddress": "kSCPropMACAddress",
    "kSCPropNetServiceOrder": "kSCPropNetServiceOrder",
    "kSCPropNetPPPCommTerminalScript": "kSCPropNetPPPCommTerminalScript",
    "kSCValNetAirPortJoinModeRanked": "kSCValNetAirPortJoinModeRanked",
    "kSCPropNetModemHoldReminder": "kSCPropNetModemHoldReminder",
    "kSCValNetIPv4ConfigMethodDHCP": "kSCValNetIPv4ConfigMethodDHCP",
    "kSCPropNetPPPUseSessionTimer": "kSCPropNetPPPUseSessionTimer",
    "kSCPropNetIPSecSharedSecret": "kSCPropNetIPSecSharedSecret",
    "kSCPropNetModemDataCompression": "kSCPropNetModemDataCompression",
    "kSCPropInterfaceName": "kSCPropInterfaceName",
    "kSCPropNetPPPCommRemoteAddress": "kSCPropNetPPPCommRemoteAddress",
    "kSCPropNetDNSSortList": "kSCPropNetDNSSortList",
    "kSCPropNetAirPortJoinMode": "kSCPropNetAirPortJoinMode",
    "kSCEntNetIPSec": "kSCEntNetIPSec",
    "kSCEntNetPPTP": "kSCEntNetPPTP",
    "kSCPropNetPPPAuthPassword": "kSCPropNetPPPAuthPassword",
    "kSCPropNetProxiesHTTPProxy": "kSCPropNetProxiesHTTPProxy",
    "kSCPropNetProxiesFTPUser": "kSCPropNetProxiesFTPUser",
    "kSCPropNetModemConnectionPersonality": "kSCPropNetModemConnectionPersonality",
    "kSCPropNetPPPAuthPrompt": "kSCPropNetPPPAuthPrompt",
    "kSCPropNetPPPACSPEnabled": "kSCPropNetPPPACSPEnabled",
    "kSCPropNetProxiesRTSPEnable": "kSCPropNetProxiesRTSPEnable",
    "kSCPropNetInterfaces": "kSCPropNetInterfaces",
    "kSCPropNetProxiesFTPPassive": "kSCPropNetProxiesFTPPassive",
    "kSCValNetPPPAuthPasswordEncryptionToken": "kSCValNetPPPAuthPasswordEncryptionToken",
    "kSCValNetIPv4ConfigMethodAutomatic": "kSCValNetIPv4ConfigMethodAutomatic",
    "kSCPropNetPPPDeviceLastCause": "kSCPropNetPPPDeviceLastCause",
    "kSCEntNetIPv4": "kSCEntNetIPv4",
    "kSCPropNetOverridePrimary": "kSCPropNetOverridePrimary",
    "kSCEntNetIPv6": "kSCEntNetIPv6",
    "kSCPropNetPPPIPCPUsePeerDNS": "kSCPropNetPPPIPCPUsePeerDNS",
    "kSCPropNetPPPStatus": "kSCPropNetPPPStatus",
    "kSCPropNetPPPCommUseTerminalScript": "kSCPropNetPPPCommUseTerminalScript",
    "kSCPropNetModemHoldReminderTime": "kSCPropNetModemHoldReminderTime",
    "kSCPropNetPPPLCPMRU": "kSCPropNetPPPLCPMRU",
    "kSCValNetIPv6ConfigMethodLinkLocal": "kSCValNetIPv6ConfigMethodLinkLocal",
}
cftypes = [
    ("SCBondStatusRef", b"^{__SCBondStatus=}", "SCBondStatusGetTypeID", None),
    ("SCDynamicStoreRef", b"^{__SCDynamicStore=}", "SCDynamicStoreGetTypeID", None),
    (
        "SCNetworkConnectionRef",
        b"^{__SCNetworkConnection=}",
        "SCNetworkConnectionGetTypeID",
        None,
    ),
    (
        "SCNetworkInterfaceRef",
        b"^{__SCNetworkInterface=}",
        "SCNetworkInterfaceGetTypeID",
        None,
    ),
    (
        "SCNetworkProtocolRef",
        b"^{__SCNetworkProtocol=}",
        "SCNetworkProtocolGetTypeID",
        None,
    ),
    (
        "SCNetworkReachabilityRef",
        b"^{__SCNetworkReachability=}",
        "SCNetworkReachabilityGetTypeID",
        None,
    ),
    (
        "SCNetworkServiceRef",
        b"^{__SCNetworkService=}",
        "SCNetworkServiceGetTypeID",
        None,
    ),
    ("SCNetworkSetRef", b"^{__SCNetworkSet=}", "SCNetworkSetGetTypeID", None),
    ("SCPreferencesRef", b"^{__SCPreferences=}", "SCPreferencesGetTypeID", None),
]
expressions = {}

# END OF FILE
