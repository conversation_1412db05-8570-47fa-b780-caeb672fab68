# This file is generated by objective.metadata
#
# Last update: Sun Nov 17 11:38:52 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$$"""
enums = """$BEAccessibilityContainerTypeAlert@1024$BEAccessibilityContainerTypeArticle@128$BEAccessibilityContainerTypeDescriptionList@2048$BEAccessibilityContainerTypeDialog@16$BEAccessibilityContainerTypeFieldset@8$BEAccessibilityContainerTypeFrame@64$BEAccessibilityContainerTypeLandmark@1$BEAccessibilityContainerTypeList@4$BEAccessibilityContainerTypeNone@0$BEAccessibilityContainerTypeS<PERSON>rollArea@512$BEAccessibilityContainerTypeSemanticGroup@256$BEAccessibilityContainerTypeTable@2$BEAccessibilityContainerTypeTree@32$BEAccessibilityPressedStateFalse@1$BEAccessibilityPressedStateMixed@3$BEAccessibilityPressedStateTrue@2$BEAccessibilityPressedStateUndefined@0$BEGestureTypeDoubleTap@3$BEGestureTypeDoubleTapAndHold@2$BEGestureTypeForceTouch@15$BEGestureTypeIMPhraseBoundaryDrag@14$BEGestureTypeLoupe@0$BEGestureTypeOneFingerDoubleTap@8$BEGestureTypeOneFingerTap@1$BEGestureTypeOneFingerTripleTap@9$BEGestureTypeTwoFingerRangedSelectGesture@11$BEGestureTypeTwoFingerSingleTap@10$BEPhraseBoundaryChanged@4$BESelectionFlagsNone@0$BESelectionFlipped@2$BESelectionTouchPhaseEnded@2$BESelectionTouchPhaseEndedMovingBackward@4$BESelectionTouchPhaseEndedMovingForward@3$BESelectionTouchPhaseEndedNotMoving@5$BESelectionTouchPhaseMoved@1$BESelectionTouchPhaseStarted@0$BEWordIsNearTap@1$"""
misc.update(
    {
        "BESelectionFlags": NewType("BESelectionFlags", int),
        "BESelectionTouchPhase": NewType("BESelectionTouchPhase", int),
        "BEGestureType": NewType("BEGestureType", int),
        "BEAccessibilityContainerType": NewType("BEAccessibilityContainerType", int),
        "BEAccessibilityPressedState": NewType("BEAccessibilityPressedState", int),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"BEDownloadMonitor",
        b"beginMonitoring:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"BEDownloadMonitor",
        b"resumeMonitoring:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"BEDownloadMonitor",
        b"useDownloadsFolderWithPlaceholderType:finalFileCreatedHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"BELayerHierarchy",
        b"layerHierarchyWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BELayerHierarchyHostingTransactionCoordinator",
        b"coordinatorWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BEMediaEnvironment",
        b"activateWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BEMediaEnvironment",
        b"makeCaptureSessionWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BEMediaEnvironment",
        b"suspendWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BENetworkingProcess",
        b"grantCapability:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"BENetworkingProcess",
        b"grantCapability:error:invalidationHandler:",
        {
            "arguments": {
                3: {"type_modifier": b"o"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
            }
        },
    )
    r(
        b"BENetworkingProcess",
        b"makeLibXPCConnectionError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BENetworkingProcess",
        b"networkProcessWithBundleID:interruptionHandler:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"BENetworkingProcess",
        b"networkProcessWithInterruptionHandler:completion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"BEProcessCapability",
        b"requestWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BERenderingProcess",
        b"grantCapability:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"BERenderingProcess",
        b"grantCapability:error:invalidationHandler:",
        {
            "arguments": {
                3: {"type_modifier": b"o"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
            }
        },
    )
    r(
        b"BERenderingProcess",
        b"makeLibXPCConnectionError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BERenderingProcess",
        b"renderingProcessWithBundleID:interruptionHandler:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"BERenderingProcess",
        b"renderingProcessWithInterruptionHandler:completion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"BEWebContentProcess",
        b"grantCapability:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"BEWebContentProcess",
        b"grantCapability:error:invalidationHandler:",
        {
            "arguments": {
                3: {"type_modifier": b"o"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
            }
        },
    )
    r(
        b"BEWebContentProcess",
        b"makeLibXPCConnectionError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"BEWebContentProcess",
        b"webContentProcessWithBundleID:interruptionHandler:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"BEWebContentProcess",
        b"webContentProcessWithInterruptionHandler:completion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSObject",
        b"accessibilityBoundsForTextMarkerRange:",
        {
            "required": True,
            "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accessibilityContentForTextMarkerRange:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"accessibilityLineEndMarkerForMarker:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"accessibilityLineEndPositionFromCurrentSelection",
        {"retval": {"type": b"q"}},
    )
    r(
        b"NSObject",
        b"accessibilityLineRangeForPosition:",
        {"retval": {"type": b"{_NSRange=QQ}"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"accessibilityLineStartMarkerForMarker:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"accessibilityLineStartPositionFromCurrentSelection",
        {"retval": {"type": b"q"}},
    )
    r(
        b"NSObject",
        b"accessibilityMarkerForPoint:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"{CGPoint=dd}"}},
        },
    )
    r(
        b"NSObject",
        b"accessibilityNextTextMarker:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"accessibilityPreviousTextMarker:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"accessibilityRangeForTextMarkerRange:",
        {
            "required": True,
            "retval": {"type": b"{_NSRange=QQ}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accessibilityTextMarkerForPosition:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"accessibilityTextMarkerRange",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"accessibilityTextMarkerRangeForCurrentSelection",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"accessibilityTextMarkerRangeForRange:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"browserAccessibilityAttributedValueInRange:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"{_NSRange=QQ}"}}},
    )
    r(b"NSObject", b"browserAccessibilityContainerType", {"retval": {"type": b"Q"}})
    r(b"NSObject", b"browserAccessibilityCurrentStatus", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"browserAccessibilityDeleteTextAtCursor:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"q"}}},
    )
    r(b"NSObject", b"browserAccessibilityHasDOMFocus", {"retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"browserAccessibilityInsertTextAtCursor:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"browserAccessibilityIsRequired", {"retval": {"type": b"Z"}})
    r(b"NSObject", b"browserAccessibilityPressedState", {"retval": {"type": b"q"}})
    r(b"NSObject", b"browserAccessibilityRoleDescription", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"browserAccessibilitySelectedTextRange",
        {"retval": {"type": b"{_NSRange=QQ}"}},
    )
    r(
        b"NSObject",
        b"browserAccessibilitySetSelectedTextRange:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"{_NSRange=QQ}"}}},
    )
    r(b"NSObject", b"browserAccessibilitySortDirection", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"browserAccessibilityValueInRange:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"{_NSRange=QQ}"}}},
    )
    r(b"NSObject", b"invalidate", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"invalidateTextEntryContextForTextInput:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"isValid", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"selectionDidChangeForTextInput:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"selectionWillChangeForTextInput:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilityContainerType:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilityCurrentStatus:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilityHasDOMFocus:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilityIsRequired:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilityPressedState:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilityRoleDescription:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setBrowserAccessibilitySortDirection:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"shouldDeferEventHandlingToSystemForTextInput:context:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"systemDidChangeSelectionForInteraction:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"systemWillChangeSelectionForInteraction:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"textInput:deferReplaceTextActionToSystem:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"textInput:setCandidateSuggestions:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "BEDownloadMonitor",
    b"initWithSourceURL:destinationURL:observedProgress:liveActivityAccessToken:",
)
objc.registerNewKeywordsFromSelector("BEMediaEnvironment", b"initWithWebPageURL:")
objc.registerNewKeywordsFromSelector("BETextSuggestion", b"initWithInputText:")
objc.registerNewKeywordsFromSelector(
    "BEWebAppManifest", b"initWithJSONData:manifestURL:"
)
protocols = {
    "BEAccessibility": objc.informal_protocol(
        "BEAccessibility",
        [
            objc.selector(
                None, b"setBrowserAccessibilityIsRequired:", b"v@:Z", isRequired=False
            ),
            objc.selector(
                None,
                b"accessibilityLineRangeForPosition:",
                b"{_NSRange=QQ}@:q",
                isRequired=False,
            ),
            objc.selector(
                None, b"browserAccessibilityCurrentStatus", b"@@:", isRequired=False
            ),
            objc.selector(
                None,
                b"browserAccessibilityInsertTextAtCursor:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"accessibilityLineStartPositionFromCurrentSelection",
                b"q@:",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"setBrowserAccessibilityContainerType:",
                b"v@:Q",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"setBrowserAccessibilityCurrentStatus:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"setBrowserAccessibilitySortDirection:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(
                None, b"setBrowserAccessibilityHasDOMFocus:", b"v@:Z", isRequired=False
            ),
            objc.selector(
                None, b"browserAccessibilityRoleDescription", b"@@:", isRequired=False
            ),
            objc.selector(
                None,
                b"accessibilityLineEndPositionFromCurrentSelection",
                b"q@:",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"browserAccessibilitySetSelectedTextRange:",
                b"v@:{_NSRange=QQ}",
                isRequired=False,
            ),
            objc.selector(
                None, b"browserAccessibilityIsRequired", b"Z@:", isRequired=False
            ),
            objc.selector(
                None,
                b"browserAccessibilitySelectedTextRange",
                b"{_NSRange=QQ}@:",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"browserAccessibilityAttributedValueInRange:",
                b"@@:{_NSRange=QQ}",
                isRequired=False,
            ),
            objc.selector(
                None, b"browserAccessibilityHasDOMFocus", b"Z@:", isRequired=False
            ),
            objc.selector(
                None, b"browserAccessibilitySortDirection", b"@@:", isRequired=False
            ),
            objc.selector(
                None, b"browserAccessibilityPressedState", b"q@:", isRequired=False
            ),
            objc.selector(
                None, b"browserAccessibilityContainerType", b"Q@:", isRequired=False
            ),
            objc.selector(
                None,
                b"setBrowserAccessibilityRoleDescription:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"browserAccessibilityDeleteTextAtCursor:",
                b"v@:q",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"browserAccessibilityValueInRange:",
                b"@@:{_NSRange=QQ}",
                isRequired=False,
            ),
            objc.selector(
                None, b"setBrowserAccessibilityPressedState:", b"v@:q", isRequired=False
            ),
        ],
    )
}
expressions = {}

# END OF FILE
