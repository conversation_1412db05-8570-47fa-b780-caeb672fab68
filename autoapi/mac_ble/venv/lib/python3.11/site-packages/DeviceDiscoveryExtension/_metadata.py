# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 10:19:48 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = (
    """$DDDeviceProtocolStringDIAL$DDDeviceProtocolStringInvalid$DDErrorDomain$"""
)
enums = """$DDDeviceCategoryAccessorySetup@6$DDDeviceCategoryDesktopComputer@5$DDDeviceCategoryHiFiSpeaker@0$DDDeviceCategoryHiFiSpeakerMultiple@1$DDDeviceCategoryLaptopComputer@4$DDDeviceCategoryTV@3$DDDeviceCategoryTVWithMediaBox@2$DDDeviceMediaPlaybackStateNoContent@0$DDDeviceMediaPlaybackStatePaused@1$DDDeviceMediaPlaybackStatePlaying@2$DDDeviceProtocolDIAL@1$DDDeviceProtocolInvalid@0$DDDeviceStateActivated@20$DDDeviceStateActivating@10$DDDeviceStateAuthorized@25$DDDeviceStateInvalid@0$DDDeviceStateInvalidating@30$DDDeviceSupportsBluetoothPairingLE@2$DDDeviceSupportsBluetoothTransportBridging@4$DDErrorCodeBadParameter@350001$DDErrorCodeInternal@350004$DDErrorCodeMissingEntitlement@350005$DDErrorCodePermission@350006$DDErrorCodeSuccess@0$DDErrorCodeTimeout@350003$DDErrorCodeUnknown@350000$DDErrorCodeUnsupported@350002$DDEventTypeDeviceChanged@42$DDEventTypeDeviceFound@40$DDEventTypeDeviceLost@41$DDEventTypeUnknown@0$"""
misc.update(
    {
        "DDDeviceSupports": NewType("DDDeviceSupports", int),
        "DDDeviceCategory": NewType("DDDeviceCategory", int),
        "DDDeviceState": NewType("DDDeviceState", int),
        "DDDeviceMediaPlaybackState": NewType("DDDeviceMediaPlaybackState", int),
        "DDEventType": NewType("DDEventType", int),
        "DDDeviceProtocol": NewType("DDDeviceProtocol", int),
        "DDErrorCode": NewType("DDErrorCode", int),
    }
)
misc.update({"DDDeviceProtocolString": NewType("DDDeviceProtocolString", str)})
misc.update({})
functions = {
    "DDDeviceProtocolToString": (b"@q",),
    "DDDeviceStateToString": (b"@q",),
    "DDDeviceMediaPlaybackStateToString": (b"@q",),
    "DDDeviceCategoryToString": (b"@q",),
    "DDEventTypeToString": (b"@q",),
}
aliases = {"dd_os_ownership": "strong"}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"DDDevice", b"setSupportsGrouping:", {"arguments": {2: {"type": b"Z"}}})
    r(b"DDDevice", b"supportsGrouping", {"retval": {"type": b"Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "DDDevice", b"initWithDisplayName:category:protocolType:identifier:"
)
objc.registerNewKeywordsFromSelector("DDDeviceEvent", b"initWithEventType:device:")
expressions = {}

# END OF FILE
