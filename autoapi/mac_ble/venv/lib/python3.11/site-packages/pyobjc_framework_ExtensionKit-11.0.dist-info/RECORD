ExtensionKit/_ExtensionKit.cpython-311-darwin.so,sha256=Sz3duc0vVm6dNFKKsf5wZ4K6rDa0PjFCG4n5aOqieH8,68072
ExtensionKit/__init__.py,sha256=ZgQ9Wpquyhqlt9QVodluEa8sJTqoxPuFfDILtz3bNug,911
ExtensionKit/__pycache__/__init__.cpython-311.pyc,,
ExtensionKit/__pycache__/_metadata.cpython-311.pyc,,
ExtensionKit/_metadata.py,sha256=j1zHZF6AZXZTNho1OGRq7F9zgs2xiS0pKfS9KBmXbYc,1175
pyobjc_framework_ExtensionKit-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_ExtensionKit-11.0.dist-info/METADATA,sha256=hQ3JxUwxkViHYwEGp_C8FjcYoGbCQdNAYSGR9FlURP8,2254
pyobjc_framework_ExtensionKit-11.0.dist-info/RECORD,,
pyobjc_framework_ExtensionKit-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_ExtensionKit-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_ExtensionKit-11.0.dist-info/top_level.txt,sha256=CNkTReFCOarLqHokGHHbxVvhqrO3X5pJn9dS8rq5Lqk,13
