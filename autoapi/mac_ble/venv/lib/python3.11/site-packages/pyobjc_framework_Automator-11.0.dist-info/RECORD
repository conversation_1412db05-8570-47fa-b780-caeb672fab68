Automator/_Automator.cpython-311-darwin.so,sha256=VsHA-mj90ncPf03Or9jssqkEtDV5QFasqcIt_en536E,68056
Automator/__init__.py,sha256=neMBouOZg8lIAPkAyIWMaJw2tToeyZ63jozm_-1GzbU,899
Automator/__pycache__/__init__.cpython-311.pyc,,
Automator/__pycache__/_metadata.cpython-311.pyc,,
Automator/_metadata.py,sha256=j5ax-pax8ihdwbizPjMLM7OiMnaB0p1O2hp9VTlCNys,6737
pyobjc_framework_Automator-11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyobjc_framework_Automator-11.0.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_Automator-11.0.dist-info/METADATA,sha256=9M6mQMJ5sQp_8j_GwNjOJd_YCxoTnzq4XMf9opufRjA,2493
pyobjc_framework_Automator-11.0.dist-info/RECORD,,
pyobjc_framework_Automator-11.0.dist-info/WHEEL,sha256=1gGdl8c7V4tzGrOfjA4xbXg5uJOisfAwei4hK84YpXE,115
pyobjc_framework_Automator-11.0.dist-info/pyobjc-build-info.txt,sha256=R5YEsJoyZ8GByMQk4k_4WWEg6V4RNO715NMQwx6YaNI,85
pyobjc_framework_Automator-11.0.dist-info/top_level.txt,sha256=Z8EqY_F6EOikiS0WygbdcnxaNGo-anA0qPt29FzSpTE,10
