# This file is generated by objective.metadata
#
# Last update: Tue <PERSON> 11 10:13:20 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$KernelManagementVersionNumber@d$KernelManagementVersionString@*$OSKernelManagementErrorDomain$"""
enums = """$OSKMErrorNotApproved@2$OSKMErrorUnknown@1$"""
misc.update({})
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"KernelManagementClient",
        b"loadExtensionsWithPaths:withError:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"KernelManagementClient",
        b"loadExtensionsWithPaths:withIdentifiers:withPersonalityNames:withNoAuth:withError:",
        {
            "retval": {"type": b"Z"},
            "arguments": {5: {"type": b"Z"}, 6: {"type_modifier": b"o"}},
        },
    )
    r(
        b"KernelManagementClient",
        b"loadExtensionsWithPaths:withNoAuth:withError:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
finally:
    objc._updatingMetadata(False)
expressions = {}

# END OF FILE
